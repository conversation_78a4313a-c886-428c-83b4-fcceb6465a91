// app.ts
interface IAppOption {
  globalData: {
    userInfo?: WechatMiniprogram.UserInfo
    userRole?: 'companion' | 'manager' | 'admin'
    currentClub?: any
    clubs?: any[]
    openid?: string
    sessionKey?: string
  }
  userInfoReadyCallback?: WechatMiniprogram.GetUserInfoSuccessCallback
}

App<IAppOption>({
  globalData: {
    userInfo: undefined,
    userRole: undefined,
    currentClub: undefined,
    clubs: [],
    openid: undefined,
    sessionKey: undefined
  },

  onLaunch() {
    // 初始化云开发
    if (!wx.cloud) {
      console.error('请使用 2.2.3 或以上的基础库以使用云能力')
    } else {
      wx.cloud.init({
        env: 'companion-gaming-system', // 云开发环境ID
        traceUser: true,
      })
    }

    // 检查登录状态
    this.checkLoginStatus()

    // 检查更新
    this.checkForUpdate()
  },

  onShow() {
    // 应用显示时检查用户状态
    this.refreshUserStatus()
  },

  // 检查登录状态
  checkLoginStatus() {
    const userInfo = wx.getStorageSync('userInfo')
    const userRole = wx.getStorageSync('userRole')

    if (userInfo && userRole) {
      this.globalData.userInfo = userInfo
      this.globalData.userRole = userRole
    } else {
      // 跳转到登录页面
      wx.reLaunch({
        url: '/pages/login/login'
      })
    }
  },

  // 刷新用户状态
  refreshUserStatus() {
    if (this.globalData.userInfo) {
      // 刷新用户俱乐部信息
      this.refreshClubInfo()
    }
  },

  // 刷新俱乐部信息
  refreshClubInfo() {
    wx.cloud.callFunction({
      name: 'getUserClubs',
      data: {},
      success: (res: any) => {
        this.globalData.clubs = res.result.clubs || []
        if (this.globalData.clubs.length > 0 && !this.globalData.currentClub) {
          this.globalData.currentClub = this.globalData.clubs[0]
        }
      },
      fail: (err) => {
        console.error('获取俱乐部信息失败:', err)
      }
    })
  },

  // 检查小程序更新
  checkForUpdate() {
    const updateManager = wx.getUpdateManager()

    updateManager.onCheckForUpdate((res) => {
      // 请求完新版本信息的回调
      if (res.hasUpdate) {
        wx.showModal({
          title: '更新提示',
          content: '新版本已经准备好，是否重启应用？',
          success: (res) => {
            if (res.confirm) {
              // 新的版本已经下载好，调用 applyUpdate 应用新版本并重启
              updateManager.applyUpdate()
            }
          }
        })
      }
    })

    updateManager.onUpdateReady(() => {
      wx.showModal({
        title: '更新提示',
        content: '新版本已经准备好，是否重启应用？',
        success: (res) => {
          if (res.confirm) {
            // 新的版本已经下载好，调用 applyUpdate 应用新版本并重启
            updateManager.applyUpdate()
          }
        }
      })
    })

    updateManager.onUpdateFailed(() => {
      // 新版本下载失败
      wx.showToast({
        title: '更新失败',
        icon: 'none'
      })
    })
  },

  // 用户登录
  login(): Promise<any> {
    return new Promise((resolve, reject) => {
      wx.login({
        success: (res) => {
          if (res.code) {
            // 调用云函数进行登录
            wx.cloud.callFunction({
              name: 'login',
              data: {
                code: res.code
              },
              success: (loginRes: any) => {
                this.globalData.openid = loginRes.result.openid
                this.globalData.sessionKey = loginRes.result.sessionKey
                resolve(loginRes.result)
              },
              fail: (err) => {
                console.error('登录失败:', err)
                reject(err)
              }
            })
          } else {
            console.error('登录失败！' + res.errMsg)
            reject(res.errMsg)
          }
        },
        fail: (err) => {
          console.error('wx.login失败:', err)
          reject(err)
        }
      })
    })
  },

  // 获取用户信息
  getUserInfo(): Promise<any> {
    return new Promise((resolve, reject) => {
      wx.getUserProfile({
        desc: '用于完善用户资料',
        success: (res) => {
          this.globalData.userInfo = res.userInfo
          wx.setStorageSync('userInfo', res.userInfo)
          resolve(res.userInfo)
        },
        fail: (err) => {
          console.error('获取用户信息失败:', err)
          reject(err)
        }
      })
    })
  }
})