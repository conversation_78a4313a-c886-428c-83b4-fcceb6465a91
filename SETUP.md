# 陪玩管理系统 - 快速启动指南

## 🚀 立即开始

### 第一步：环境准备

1. **下载微信开发者工具**
   - 访问 [微信开发者工具官网](https://developers.weixin.qq.com/miniprogram/dev/devtools/download.html)
   - 下载并安装最新版本

2. **注册小程序账号**
   - 访问 [微信公众平台](https://mp.weixin.qq.com/)
   - 注册小程序账号并获取 AppID

3. **开通云开发**
   - 在微信开发者工具中开通云开发服务
   - 创建云开发环境

### 第二步：项目配置

1. **修改项目配置**
   ```json
   // project.config.json
   {
     "appid": "你的小程序AppID"
   }
   ```

2. **配置云开发环境**
   ```typescript
   // app.ts
   wx.cloud.init({
     env: '你的云开发环境ID',
     traceUser: true,
   })
   ```

### 第三步：数据库初始化

在云开发控制台创建以下数据库集合：

```bash
# 核心集合
users                    # 用户信息
clubs                    # 俱乐部信息
club_members            # 俱乐部成员

# 订单集合
game_orders             # 游戏订单
gift_orders             # 礼物订单
fine_records            # 罚款记录
group_fee_records       # 团费记录
deposit_records         # 存单记录
predeposit_records      # 预存记录
client_acquisition_records  # 客户获取记录

# 系统集合
announcements           # 公告
financial_settlements   # 财务结算
```

### 第四步：云函数部署

1. **安装云函数依赖**
   ```bash
   cd cloudfunctions/login
   npm install
   
   cd ../createOrUpdateUser
   npm install
   
   cd ../getUserClubs
   npm install
   
   cd ../createGameOrder
   npm install
   
   cd ../getOrders
   npm install
   
   cd ../getTodayStats
   npm install
   ```

2. **部署云函数**
   - 在微信开发者工具中右键每个云函数文件夹
   - 选择"上传并部署：云端安装依赖"

### 第五步：数据库权限配置

将 `database/database.rules.json` 中的权限规则应用到云数据库：

1. 打开云开发控制台
2. 进入数据库 → 数据库设置 → 权限设置
3. 将规则文件内容复制到权限设置中

### 第六步：运行测试

1. **编译项目**
   - 在微信开发者工具中点击"编译"

2. **测试功能**
   - 测试登录功能
   - 测试角色选择
   - 测试基本页面跳转

## 📋 功能检查清单

### ✅ 已完成功能

- [x] 用户登录和角色选择
- [x] 陪玩师首页和数据概览
- [x] 报单管理和创建
- [x] 订单详情查看
- [x] 收益统计分析
- [x] 个人中心管理
- [x] 管理员首页
- [x] 俱乐部管理基础功能
- [x] 云函数后端支持
- [x] 数据库设计和权限

### 🔄 需要完善的功能

- [ ] 管理员报单审核页面
- [ ] 成员管理页面
- [ ] 财务管理页面
- [ ] 公告管理功能
- [ ] 通知推送系统
- [ ] 图表组件集成

## 🛠️ 开发建议

### 1. 优先完成核心流程

建议按以下顺序完善功能：

1. **完善登录和用户管理**
   - 测试微信登录流程
   - 完善用户信息管理

2. **完善报单功能**
   - 测试报单创建流程
   - 完善各种报单类型

3. **完善管理功能**
   - 实现报单审核
   - 实现成员管理

4. **完善统计功能**
   - 集成图表组件
   - 完善数据统计

### 2. 测试数据准备

创建测试数据：

```javascript
// 在云开发控制台数据库中手动添加测试数据

// 测试俱乐部
{
  name: "测试俱乐部",
  description: "这是一个测试俱乐部",
  ownerId: "用户ID",
  status: "active",
  settings: {
    maxMembers: 100,
    commissionRate: 0.15,
    autoApprove: false
  }
}

// 测试成员关系
{
  clubId: "俱乐部ID",
  userId: "用户ID", 
  role: "member",
  status: "active"
}
```

### 3. 调试技巧

1. **查看云函数日志**
   - 在云开发控制台查看云函数执行日志
   - 使用 console.log 输出调试信息

2. **数据库调试**
   - 在云开发控制台直接查询数据库
   - 检查数据结构是否正确

3. **网络请求调试**
   - 在开发者工具中查看网络请求
   - 检查API调用是否成功

## 🚨 常见问题

### 1. 云函数调用失败

**问题**: 云函数调用返回错误
**解决**: 
- 检查云函数是否正确部署
- 检查云开发环境ID是否正确
- 查看云函数日志排查错误

### 2. 数据库权限错误

**问题**: 数据库操作被拒绝
**解决**:
- 检查数据库权限规则是否正确配置
- 确认用户身份验证是否成功

### 3. 页面跳转失败

**问题**: 页面跳转时提示页面不存在
**解决**:
- 检查 app.json 中页面路径是否正确
- 确认页面文件是否存在

### 4. 样式显示异常

**问题**: 页面样式不正确
**解决**:
- 检查 wxss 文件是否正确引入
- 确认样式类名是否正确

## 📞 获取帮助

如果遇到问题：

1. **查看文档**
   - 阅读 README.md
   - 查看 docs/ 目录下的详细文档

2. **检查代码**
   - 对比示例代码
   - 检查配置是否正确

3. **调试工具**
   - 使用微信开发者工具的调试功能
   - 查看控制台错误信息

## 🎉 部署成功

当所有功能测试通过后：

1. **提交代码审核**
   - 在微信开发者工具中提交代码
   - 等待微信审核通过

2. **发布上线**
   - 审核通过后发布小程序
   - 开始正式使用

恭喜！您的陪玩管理系统已经可以正常使用了！
