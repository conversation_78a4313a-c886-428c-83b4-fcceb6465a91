// 部署脚本
const fs = require('fs')
const path = require('path')

console.log('🚀 开始部署陪玩管理系统...')

// 检查必要文件
const requiredFiles = [
  'app.json',
  'app.ts', 
  'app.wxss',
  'project.config.json'
]

console.log('📋 检查必要文件...')
for (const file of requiredFiles) {
  if (!fs.existsSync(file)) {
    console.error(`❌ 缺少必要文件: ${file}`)
    process.exit(1)
  }
}
console.log('✅ 必要文件检查完成')

// 检查云函数
const cloudFunctions = [
  'login',
  'createOrUpdateUser',
  'getUserClubs',
  'createGameOrder',
  'getOrders',
  'getTodayStats'
]

console.log('☁️ 检查云函数...')
for (const func of cloudFunctions) {
  const funcPath = path.join('cloudfunctions', func)
  if (!fs.existsSync(funcPath)) {
    console.error(`❌ 缺少云函数: ${func}`)
    process.exit(1)
  }
  
  const packagePath = path.join(funcPath, 'package.json')
  if (!fs.existsSync(packagePath)) {
    console.error(`❌ 云函数 ${func} 缺少 package.json`)
    process.exit(1)
  }
}
console.log('✅ 云函数检查完成')

// 检查页面文件
const pages = [
  'pages/login/login',
  'pages/companion/home/<USER>',
  'pages/companion/orders/orders',
  'pages/companion/create-order/create-order',
  'pages/manager/home/<USER>'
]

console.log('📱 检查页面文件...')
for (const page of pages) {
  const requiredPageFiles = ['.wxml', '.ts', '.wxss', '.json']
  for (const ext of requiredPageFiles) {
    const filePath = page + ext
    if (!fs.existsSync(filePath)) {
      console.warn(`⚠️ 页面文件可能缺失: ${filePath}`)
    }
  }
}
console.log('✅ 页面文件检查完成')

// 生成部署清单
const deployManifest = {
  timestamp: new Date().toISOString(),
  version: '1.0.0',
  files: {
    pages: pages.length,
    cloudFunctions: cloudFunctions.length,
    utils: fs.existsSync('utils') ? fs.readdirSync('utils').length : 0
  },
  cloudFunctions: cloudFunctions,
  pages: pages
}

fs.writeFileSync('deploy-manifest.json', JSON.stringify(deployManifest, null, 2))
console.log('📄 生成部署清单: deploy-manifest.json')

console.log('\n🎉 部署检查完成！')
console.log('\n📋 部署清单:')
console.log(`- 页面数量: ${deployManifest.files.pages}`)
console.log(`- 云函数数量: ${deployManifest.files.cloudFunctions}`)
console.log(`- 工具文件数量: ${deployManifest.files.utils}`)

console.log('\n📝 接下来的步骤:')
console.log('1. 在微信开发者工具中打开项目')
console.log('2. 配置云开发环境ID')
console.log('3. 创建数据库集合')
console.log('4. 部署云函数')
console.log('5. 配置数据库权限')
console.log('6. 测试功能')
console.log('7. 提交审核')

console.log('\n✨ 祝您部署顺利！')
