<!--pages/companion/earnings/earnings.wxml-->
<view class="container">
  <!-- 时间筛选 -->
  <view class="filter-section">
    <view class="time-filter">
      <picker 
        mode="selector" 
        range="{{periodOptions}}" 
        range-key="text"
        value="{{selectedPeriodIndex}}"
        bindchange="onPeriodChange"
      >
        <view class="filter-btn">
          {{periodOptions[selectedPeriodIndex].text}}
          <image class="filter-arrow" src="/assets/icons/arrow-down.png"></image>
        </view>
      </picker>
      
      <view class="custom-date" wx:if="{{showCustomDate}}">
        <picker 
          mode="date" 
          value="{{customStartDate}}"
          bindchange="onStartDateChange"
        >
          <view class="date-input">{{customStartDate}}</view>
        </picker>
        <text class="date-separator">至</text>
        <picker 
          mode="date" 
          value="{{customEndDate}}"
          bindchange="onEndDateChange"
        >
          <view class="date-input">{{customEndDate}}</view>
        </picker>
      </view>
    </view>
  </view>

  <!-- 收益概览 -->
  <view class="summary-section">
    <view class="summary-card">
      <view class="summary-title">收益概览</view>
      
      <view class="summary-item main">
        <text class="summary-label">净收益</text>
        <text class="summary-value positive">¥{{earningsSummary.netEarnings}}</text>
      </view>
      
      <view class="summary-grid">
        <view class="summary-item">
          <text class="summary-label">总收入</text>
          <text class="summary-value">¥{{earningsSummary.totalRevenue}}</text>
        </view>
        
        <view class="summary-item">
          <text class="summary-label">游戏收益</text>
          <text class="summary-value">¥{{earningsSummary.gameEarnings}}</text>
        </view>
        
        <view class="summary-item">
          <text class="summary-label">礼物收益</text>
          <text class="summary-value">¥{{earningsSummary.giftEarnings}}</text>
        </view>
        
        <view class="summary-item">
          <text class="summary-label">其他收益</text>
          <text class="summary-value">¥{{earningsSummary.otherEarnings}}</text>
        </view>
        
        <view class="summary-item">
          <text class="summary-label">扣除罚款</text>
          <text class="summary-value negative">-¥{{earningsSummary.totalFines}}</text>
        </view>
        
        <view class="summary-item">
          <text class="summary-label">扣除团费</text>
          <text class="summary-value negative">-¥{{earningsSummary.totalFees}}</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 收益趋势图 -->
  <view class="chart-section">
    <view class="section-title">收益趋势</view>
    <view class="chart-container">
      <!-- 这里可以集成图表组件，暂时用占位符 -->
      <view class="chart-placeholder">
        <text class="chart-text">收益趋势图</text>
        <view class="trend-info">
          <text class="trend-item">最高单日: ¥{{trendData.maxDaily}}</text>
          <text class="trend-item">平均单日: ¥{{trendData.avgDaily}}</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 收益构成 -->
  <view class="composition-section">
    <view class="section-title">收益构成</view>
    <view class="composition-chart">
      <!-- 饼图占位符 -->
      <view class="pie-chart-placeholder">
        <text class="chart-text">收益构成饼图</text>
      </view>
      
      <view class="composition-legend">
        <view class="legend-item">
          <view class="legend-color game"></view>
          <text class="legend-text">游戏单 {{compositionData.gamePercentage}}%</text>
        </view>
        
        <view class="legend-item">
          <view class="legend-color gift"></view>
          <text class="legend-text">礼物单 {{compositionData.giftPercentage}}%</text>
        </view>
        
        <view class="legend-item">
          <view class="legend-color other"></view>
          <text class="legend-text">其他 {{compositionData.otherPercentage}}%</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 排名信息 -->
  <view class="ranking-section">
    <view class="section-title">排名信息</view>
    <view class="ranking-card">
      <view class="ranking-item">
        <text class="ranking-label">俱乐部排名</text>
        <text class="ranking-value">第{{rankingInfo.clubRank}}名 / {{rankingInfo.totalMembers}}人</text>
      </view>
      
      <view class="ranking-item">
        <text class="ranking-label">本期完成订单</text>
        <text class="ranking-value">{{rankingInfo.completedOrders}}单</text>
      </view>
      
      <view class="ranking-item">
        <text class="ranking-label">客户满意度</text>
        <text class="ranking-value">{{rankingInfo.satisfaction}}%</text>
      </view>
    </view>
  </view>

  <!-- 详细记录 -->
  <view class="records-section">
    <view class="section-title">
      <text class="title">详细记录</text>
      <text class="more" bindtap="viewAllRecords">查看全部</text>
    </view>
    
    <view class="record-list" wx:if="{{earningsRecords.length > 0}}">
      <view 
        class="record-item" 
        wx:for="{{earningsRecords}}" 
        wx:key="id"
        bindtap="viewRecordDetail"
        data-id="{{item.id}}"
      >
        <view class="record-info">
          <view class="record-header">
            <text class="record-type">{{item.typeText}}</text>
            <text class="record-date">{{item.date}}</text>
          </view>
          
          <text class="record-desc">{{item.description}}</text>
          
          <view class="record-footer">
            <text class="record-status status-{{item.status}}">{{item.statusText}}</text>
            <text class="record-amount {{item.amountClass}}">{{item.amountText}}</text>
          </view>
        </view>
      </view>
    </view>
    
    <view class="empty-state" wx:else>
      <text class="empty-text">暂无收益记录</text>
    </view>
  </view>
</view>
