<!--pages/companion/orders/orders.wxml-->
<view class="container">
  <!-- 筛选栏 -->
  <view class="filter-bar">
    <view class="filter-item">
      <picker 
        mode="selector" 
        range="{{statusOptions}}" 
        range-key="text"
        value="{{selectedStatusIndex}}"
        bindchange="onStatusChange"
      >
        <view class="filter-text">
          {{statusOptions[selectedStatusIndex].text}}
          <image class="filter-arrow" src="/assets/icons/arrow-down.png"></image>
        </view>
      </picker>
    </view>
    
    <view class="filter-item">
      <picker 
        mode="selector" 
        range="{{typeOptions}}" 
        range-key="text"
        value="{{selectedTypeIndex}}"
        bindchange="onTypeChange"
      >
        <view class="filter-text">
          {{typeOptions[selectedTypeIndex].text}}
          <image class="filter-arrow" src="/assets/icons/arrow-down.png"></image>
        </view>
      </picker>
    </view>
    
    <view class="search-box">
      <input 
        class="search-input" 
        placeholder="搜索订单"
        value="{{searchKeyword}}"
        bindinput="onSearchInput"
        confirm-type="search"
        bindconfirm="onSearch"
      />
      <image class="search-icon" src="/assets/icons/search.png"></image>
    </view>
  </view>

  <!-- 新建按钮 -->
  <view class="create-btn" bindtap="showCreateOptions">
    <image class="create-icon" src="/assets/icons/add.png"></image>
    <text class="create-text">新建报单</text>
  </view>

  <!-- 订单列表 -->
  <view class="order-list" wx:if="{{orders.length > 0}}">
    <view 
      class="order-item" 
      wx:for="{{orders}}" 
      wx:key="id"
      bindtap="viewOrderDetail"
      data-id="{{item.id}}"
    >
      <!-- 订单头部 -->
      <view class="order-header">
        <view class="order-type-badge type-{{item.orderType}}">
          {{item.typeText}}
        </view>
        <view class="order-id">#{{item.orderNumber || item.id.slice(-6)}}</view>
        <view class="order-status status-{{item.status}}">
          {{item.statusText}}
        </view>
      </view>

      <!-- 订单内容 -->
      <view class="order-content">
        <text class="order-title">{{item.title}}</text>
        <text class="order-desc">{{item.description}}</text>
      </view>

      <!-- 订单详情 -->
      <view class="order-details" wx:if="{{item.orderType === 'gaming'}}">
        <view class="detail-item">
          <text class="detail-label">游戏:</text>
          <text class="detail-value">{{item.gameInfo.gameName}}</text>
        </view>
        <view class="detail-item">
          <text class="detail-label">时长:</text>
          <text class="detail-value">{{item.gameInfo.duration}}分钟</text>
        </view>
        <view class="detail-item" wx:if="{{item.clientInfo.clientName}}">
          <text class="detail-label">客户:</text>
          <text class="detail-value">{{item.clientInfo.clientName}}</text>
        </view>
      </view>

      <view class="order-details" wx:elif="{{item.orderType === 'gift'}}">
        <view class="detail-item">
          <text class="detail-label">礼物:</text>
          <text class="detail-value">{{item.giftInfo.giftName}} x{{item.giftInfo.quantity}}</text>
        </view>
        <view class="detail-item" wx:if="{{item.clientInfo.clientName}}">
          <text class="detail-label">客户:</text>
          <text class="detail-value">{{item.clientInfo.clientName}}</text>
        </view>
      </view>

      <view class="order-details" wx:elif="{{item.orderType === 'fine'}}">
        <view class="detail-item">
          <text class="detail-label">原因:</text>
          <text class="detail-value">{{item.fineInfo.reason}}</text>
        </view>
        <view class="detail-item">
          <text class="detail-label">类别:</text>
          <text class="detail-value">{{item.fineInfo.categoryText}}</text>
        </view>
      </view>

      <!-- 订单底部 -->
      <view class="order-footer">
        <view class="order-time">
          <text class="time-label">创建时间:</text>
          <text class="time-value">{{item.createdAt}}</text>
        </view>
        
        <view class="order-amount">
          <text class="amount-label">金额:</text>
          <text class="amount-value {{item.amountClass}}">{{item.amountText}}</text>
        </view>
      </view>

      <!-- 操作按钮 -->
      <view class="order-actions" wx:if="{{item.showActions}}">
        <button 
          class="action-btn btn-secondary" 
          size="mini"
          bindtap="editOrder"
          data-id="{{item.id}}"
          catchtap="stopPropagation"
          wx:if="{{item.status === 'pending'}}"
        >
          编辑
        </button>
        
        <button 
          class="action-btn btn-danger" 
          size="mini"
          bindtap="deleteOrder"
          data-id="{{item.id}}"
          catchtap="stopPropagation"
          wx:if="{{item.status === 'pending'}}"
        >
          删除
        </button>
        
        <button 
          class="action-btn btn-primary" 
          size="mini"
          bindtap="resubmitOrder"
          data-id="{{item.id}}"
          catchtap="stopPropagation"
          wx:if="{{item.status === 'rejected'}}"
        >
          重新提交
        </button>
      </view>
    </view>
  </view>

  <!-- 空状态 -->
  <view class="empty-state" wx:else>
    <image class="empty-icon" src="/assets/icons/empty-orders.png"></image>
    <text class="empty-text">暂无订单记录</text>
    <button class="empty-btn" bindtap="showCreateOptions">创建第一个报单</button>
  </view>

  <!-- 加载更多 -->
  <view class="load-more" wx:if="{{hasMore && orders.length > 0}}">
    <button class="load-more-btn" bindtap="loadMore" loading="{{loading}}">
      {{loading ? '加载中...' : '加载更多'}}
    </button>
  </view>
</view>

<!-- 创建选项弹窗 -->
<view class="create-modal" wx:if="{{showCreateModal}}" bindtap="hideCreateOptions">
  <view class="modal-content" catchtap="stopPropagation">
    <view class="modal-header">
      <text class="modal-title">选择报单类型</text>
      <image class="close-icon" src="/assets/icons/close.png" bindtap="hideCreateOptions"></image>
    </view>
    
    <view class="create-options">
      <view 
        class="create-option"
        wx:for="{{createOptions}}"
        wx:key="type"
        bindtap="createOrder"
        data-type="{{item.type}}"
      >
        <image class="option-icon" src="{{item.icon}}"></image>
        <view class="option-info">
          <text class="option-name">{{item.name}}</text>
          <text class="option-desc">{{item.desc}}</text>
        </view>
        <image class="option-arrow" src="/assets/icons/arrow-right.png"></image>
      </view>
    </view>
  </view>
</view>
