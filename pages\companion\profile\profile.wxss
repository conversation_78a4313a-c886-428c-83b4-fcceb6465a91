/* pages/companion/profile/profile.wxss */

.container {
  background-color: #f8f8f8;
  min-height: 100vh;
}

/* 用户信息 */
.profile-section {
  padding: 20rpx 30rpx;
}

.user-card {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 40rpx 30rpx;
  display: flex;
  align-items: center;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  position: relative;
}

.avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  margin-right: 30rpx;
}

.user-details {
  flex: 1;
}

.nickname {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 8rpx;
  display: block;
}

.role {
  font-size: 26rpx;
  color: #1AAD19;
  background-color: #f0f9f0;
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  margin-bottom: 12rpx;
  display: inline-block;
}

.join-date {
  font-size: 24rpx;
  color: #999;
  display: block;
}

.edit-btn {
  position: absolute;
  top: 30rpx;
  right: 30rpx;
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.edit-icon {
  width: 32rpx;
  height: 32rpx;
}

/* 统计信息 */
.stats-section {
  padding: 0 30rpx 20rpx;
}

.stats-grid {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 30rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.stats-item {
  text-align: center;
}

.stats-number {
  font-size: 48rpx;
  font-weight: 700;
  color: #1AAD19;
  margin-bottom: 8rpx;
  display: block;
}

.stats-label {
  font-size: 24rpx;
  color: #666;
}

/* 功能菜单 */
.menu-section {
  padding: 0 30rpx;
}

.menu-group {
  background-color: #fff;
  border-radius: 16rpx;
  margin-bottom: 20rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.menu-item {
  display: flex;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
  position: relative;
}

.menu-item:last-child {
  border-bottom: none;
}

.menu-icon {
  width: 60rpx;
  height: 60rpx;
  margin-right: 30rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.menu-icon image {
  width: 40rpx;
  height: 40rpx;
}

.menu-text {
  flex: 1;
  font-size: 30rpx;
  color: #333;
}

.menu-badge {
  background-color: #FF6B6B;
  color: #fff;
  font-size: 20rpx;
  padding: 4rpx 8rpx;
  border-radius: 20rpx;
  min-width: 32rpx;
  text-align: center;
  margin-right: 20rpx;
}

.menu-arrow {
  width: 40rpx;
  height: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.menu-arrow image {
  width: 24rpx;
  height: 24rpx;
}

/* 退出登录 */
.logout-section {
  padding: 20rpx 30rpx;
}

.logout-btn {
  width: 100%;
  height: 88rpx;
  background-color: #fff;
  color: #FF6B6B;
  border: 2rpx solid #FF6B6B;
  border-radius: 12rpx;
  font-size: 32rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 版本信息 */
.version-section {
  text-align: center;
  padding: 20rpx;
}

.version-text {
  font-size: 24rpx;
  color: #999;
}

/* 编辑弹窗 */
.edit-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  background-color: #fff;
  border-radius: 16rpx;
  width: 600rpx;
  max-height: 80vh;
  overflow: hidden;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.modal-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.close-icon {
  width: 40rpx;
  height: 40rpx;
}

.modal-body {
  padding: 30rpx;
}

.form-group {
  margin-bottom: 30rpx;
}

.form-label {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 12rpx;
  display: block;
}

.form-input {
  width: 100%;
  padding: 20rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 8rpx;
  font-size: 28rpx;
  background-color: #fff;
}

.form-input:focus {
  border-color: #1AAD19;
}

.modal-footer {
  display: flex;
  padding: 30rpx;
  gap: 20rpx;
  border-top: 1rpx solid #f0f0f0;
}

.modal-btn {
  flex: 1;
  height: 80rpx;
  border-radius: 12rpx;
  font-size: 28rpx;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-btn.cancel {
  background-color: #f0f0f0;
  color: #333;
}

.modal-btn.confirm {
  background-color: #1AAD19;
  color: #fff;
}
