// pages/companion/create-order/create-order.ts
import api from '../../../utils/api'
import { showError, showSuccess, formatAmount } from '../../../utils/util'

const app = getApp<IAppOption>()

Page({
  data: {
    orderType: '',
    pageTitle: '',
    isEdit: false,
    orderId: '',
    submitting: false,
    
    // 表单数据
    formData: {
      // 游戏单
      gameIndex: 0,
      gameName: '',
      gameModeIndex: 0,
      gameMode: '',
      duration: '',
      rank: '',
      
      // 礼物单
      giftName: '',
      giftTypeIndex: 0,
      giftType: '',
      quantity: '1',
      unitPrice: '',
      
      // 通用
      clientName: '',
      clientContact: '',
      totalAmount: '',
      amount: '',
      purpose: '',
      maturityDate: '',
      gameAccount: '',
      expectedUsage: '',
      clientSource: '',
      gamePreference: '',
      expectedRevenue: '',
      notes: '',
      images: [] as string[]
    },
    
    // 计算结果
    calculatedTotal: '0.00',
    calculatedEarning: '0.00',
    calculatedCommission: '0.00',
    
    // 选项数据
    gameOptions: [
      { name: '王者荣耀' },
      { name: '和平精英' },
      { name: '英雄联盟手游' },
      { name: '原神' },
      { name: '其他' }
    ],
    
    gameModeOptions: ['排位赛', '匹配赛', '娱乐模式', '其他'],
    
    giftTypeOptions: ['虚拟礼物', '实物礼品', '游戏道具', '其他'],
    
    depositTypeOptions: ['保证金', '押金', '预付款', '其他']
  },

  onLoad(options: any) {
    const { type, id } = options
    
    if (!type) {
      showError('订单类型不能为空')
      wx.navigateBack()
      return
    }

    this.setData({
      orderType: type,
      pageTitle: this.getPageTitle(type),
      isEdit: !!id,
      orderId: id || ''
    })

    if (id) {
      this.loadOrderDetail(id)
    }
  },

  // 获取页面标题
  getPageTitle(type: string) {
    const titleMap: { [key: string]: string } = {
      'gaming': '创建游戏单',
      'gift': '创建礼物单',
      'deposit': '创建存单',
      'predeposit': '创建预存',
      'client_acquisition': '创建拉老板记录'
    }
    return titleMap[type] || '创建报单'
  },

  // 加载订单详情（编辑模式）
  async loadOrderDetail(orderId: string) {
    try {
      const res = await api.callFunction('getOrderDetail', { orderId })
      if (res.success) {
        this.fillFormData(res.data.order)
      }
    } catch (error) {
      console.error('加载订单详情失败:', error)
      showError('加载订单详情失败')
    }
  },

  // 填充表单数据
  fillFormData(order: any) {
    const formData = { ...this.data.formData }
    
    switch (order.orderType) {
      case 'gaming':
        formData.gameName = order.gameInfo?.gameName || ''
        formData.gameMode = order.gameInfo?.gameMode || ''
        formData.duration = order.gameInfo?.duration?.toString() || ''
        formData.rank = order.gameInfo?.rank || ''
        formData.totalAmount = order.financial?.totalAmount?.toString() || ''
        break
      case 'gift':
        formData.giftName = order.giftInfo?.giftName || ''
        formData.giftType = order.giftInfo?.giftType || ''
        formData.quantity = order.giftInfo?.quantity?.toString() || '1'
        formData.unitPrice = order.giftInfo?.unitPrice?.toString() || ''
        break
    }
    
    // 通用字段
    formData.clientName = order.clientInfo?.clientName || ''
    formData.clientContact = order.clientInfo?.clientContact || ''
    formData.notes = order.notes || ''
    formData.images = order.attachments || []
    
    this.setData({ formData })
    this.calculateAmounts()
  },

  // 游戏选择
  onGameChange(e: any) {
    const index = e.detail.value
    const game = this.data.gameOptions[index]
    this.setData({
      'formData.gameIndex': index,
      'formData.gameName': game.name
    })
  },

  // 游戏模式选择
  onGameModeChange(e: any) {
    const index = e.detail.value
    const mode = this.data.gameModeOptions[index]
    this.setData({
      'formData.gameModeIndex': index,
      'formData.gameMode': mode
    })
  },

  // 输入事件处理
  onDurationInput(e: any) {
    this.setData({ 'formData.duration': e.detail.value })
  },

  onRankInput(e: any) {
    this.setData({ 'formData.rank': e.detail.value })
  },

  onClientNameInput(e: any) {
    this.setData({ 'formData.clientName': e.detail.value })
  },

  onClientContactInput(e: any) {
    this.setData({ 'formData.clientContact': e.detail.value })
  },

  onTotalAmountInput(e: any) {
    this.setData({ 'formData.totalAmount': e.detail.value })
    this.calculateAmounts()
  },

  onGiftNameInput(e: any) {
    this.setData({ 'formData.giftName': e.detail.value })
  },

  onGiftTypeChange(e: any) {
    const index = e.detail.value
    const type = this.data.giftTypeOptions[index]
    this.setData({
      'formData.giftTypeIndex': index,
      'formData.giftType': type
    })
  },

  onQuantityInput(e: any) {
    this.setData({ 'formData.quantity': e.detail.value })
    this.calculateAmounts()
  },

  onUnitPriceInput(e: any) {
    this.setData({ 'formData.unitPrice': e.detail.value })
    this.calculateAmounts()
  },

  onAmountInput(e: any) {
    this.setData({ 'formData.amount': e.detail.value })
  },

  onPurposeInput(e: any) {
    this.setData({ 'formData.purpose': e.detail.value })
  },

  onMaturityDateChange(e: any) {
    this.setData({ 'formData.maturityDate': e.detail.value })
  },

  onGameAccountInput(e: any) {
    this.setData({ 'formData.gameAccount': e.detail.value })
  },

  onExpectedUsageInput(e: any) {
    this.setData({ 'formData.expectedUsage': e.detail.value })
  },

  onClientSourceInput(e: any) {
    this.setData({ 'formData.clientSource': e.detail.value })
  },

  onGamePreferenceInput(e: any) {
    this.setData({ 'formData.gamePreference': e.detail.value })
  },

  onExpectedRevenueInput(e: any) {
    this.setData({ 'formData.expectedRevenue': e.detail.value })
  },

  onNotesInput(e: any) {
    this.setData({ 'formData.notes': e.detail.value })
  },

  // 计算金额
  calculateAmounts() {
    const currentClub = app.globalData.currentClub
    if (!currentClub) return

    const commissionRate = currentClub.settings?.commissionRate || 0.15
    let totalAmount = 0

    if (this.data.orderType === 'gaming') {
      totalAmount = parseFloat(this.data.formData.totalAmount) || 0
    } else if (this.data.orderType === 'gift') {
      const quantity = parseInt(this.data.formData.quantity) || 1
      const unitPrice = parseFloat(this.data.formData.unitPrice) || 0
      totalAmount = quantity * unitPrice
    }

    const clubCommission = totalAmount * commissionRate
    const companionEarning = totalAmount - clubCommission

    this.setData({
      calculatedTotal: formatAmount(totalAmount, false),
      calculatedEarning: formatAmount(companionEarning, false),
      calculatedCommission: formatAmount(clubCommission, false)
    })
  },

  // 选择图片
  chooseImage() {
    wx.chooseImage({
      count: 3 - this.data.formData.images.length,
      sizeType: ['compressed'],
      sourceType: ['album', 'camera'],
      success: (res) => {
        this.uploadImages(res.tempFilePaths)
      }
    })
  },

  // 上传图片
  async uploadImages(filePaths: string[]) {
    const uploadPromises = filePaths.map(async (filePath) => {
      const cloudPath = `orders/${Date.now()}_${Math.random().toString(36).substr(2, 9)}.jpg`
      const result = await api.uploadFile(filePath, cloudPath)
      return result.success ? result.data.fileID : null
    })

    try {
      const results = await Promise.all(uploadPromises)
      const validResults = results.filter(result => result !== null)
      
      this.setData({
        'formData.images': [...this.data.formData.images, ...validResults]
      })
    } catch (error) {
      console.error('上传图片失败:', error)
      showError('上传图片失败')
    }
  },

  // 删除图片
  deleteImage(e: any) {
    const index = e.currentTarget.dataset.index
    const images = [...this.data.formData.images]
    images.splice(index, 1)
    this.setData({ 'formData.images': images })
  },

  // 提交表单
  async onSubmit() {
    if (!this.validateForm()) return

    this.setData({ submitting: true })

    try {
      const orderData = this.buildOrderData()
      let res

      if (this.data.isEdit) {
        res = await api.callFunction('updateOrder', {
          orderId: this.data.orderId,
          orderData
        })
      } else {
        res = await api.callFunction(`create${this.getOrderTypeName()}Order`, {
          [`${this.getOrderTypeName().toLowerCase()}OrderData`]: orderData
        })
      }

      if (res.success) {
        showSuccess(this.data.isEdit ? '更新成功' : '提交成功')
        wx.navigateBack()
      }
    } catch (error) {
      console.error('提交失败:', error)
      showError('提交失败')
    } finally {
      this.setData({ submitting: false })
    }
  },

  // 验证表单
  validateForm() {
    const { formData, orderType } = this.data

    switch (orderType) {
      case 'gaming':
        if (!formData.gameName) {
          showError('请选择游戏名称')
          return false
        }
        if (!formData.gameMode) {
          showError('请选择游戏模式')
          return false
        }
        if (!formData.duration) {
          showError('请输入游戏时长')
          return false
        }
        if (!formData.clientName) {
          showError('请输入客户昵称')
          return false
        }
        if (!formData.totalAmount) {
          showError('请输入订单金额')
          return false
        }
        break
      case 'gift':
        if (!formData.giftName) {
          showError('请输入礼物名称')
          return false
        }
        if (!formData.quantity) {
          showError('请输入数量')
          return false
        }
        if (!formData.unitPrice) {
          showError('请输入单价')
          return false
        }
        if (!formData.clientName) {
          showError('请输入客户昵称')
          return false
        }
        break
    }

    return true
  },

  // 构建订单数据
  buildOrderData() {
    const { formData, orderType } = this.data
    const currentClub = app.globalData.currentClub

    const baseData = {
      clubId: currentClub.id,
      notes: formData.notes,
      attachments: formData.images
    }

    switch (orderType) {
      case 'gaming':
        return {
          ...baseData,
          gameName: formData.gameName,
          gameMode: formData.gameMode,
          duration: parseInt(formData.duration),
          rank: formData.rank,
          clientName: formData.clientName,
          clientContact: formData.clientContact,
          totalAmount: parseFloat(formData.totalAmount)
        }
      case 'gift':
        return {
          ...baseData,
          giftName: formData.giftName,
          giftType: formData.giftType,
          quantity: parseInt(formData.quantity),
          unitPrice: parseFloat(formData.unitPrice),
          clientName: formData.clientName,
          clientContact: formData.clientContact
        }
      default:
        return baseData
    }
  },

  // 获取订单类型名称
  getOrderTypeName() {
    const typeMap: { [key: string]: string } = {
      'gaming': 'Game',
      'gift': 'Gift',
      'deposit': 'Deposit',
      'predeposit': 'Predeposit',
      'client_acquisition': 'ClientAcquisition'
    }
    return typeMap[this.data.orderType] || 'Order'
  },

  // 取消
  onCancel() {
    wx.navigateBack()
  }
})
