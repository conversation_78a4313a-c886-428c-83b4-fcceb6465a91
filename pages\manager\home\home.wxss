/* pages/manager/home/<USER>/

.container {
  background-color: #f8f8f8;
  min-height: 100vh;
}

/* 头部区域 */
.header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20rpx 30rpx 40rpx;
  color: #fff;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.user-info {
  display: flex;
  align-items: center;
}

.avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  margin-right: 20rpx;
  border: 3rpx solid rgba(255, 255, 255, 0.3);
}

.user-details {
  display: flex;
  flex-direction: column;
}

.nickname {
  font-size: 32rpx;
  font-weight: 600;
  margin-bottom: 8rpx;
}

.role {
  font-size: 24rpx;
  opacity: 0.8;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.club-info {
  display: flex;
  align-items: center;
  background-color: rgba(255, 255, 255, 0.2);
  padding: 12rpx 20rpx;
  border-radius: 20rpx;
}

.club-name {
  font-size: 24rpx;
  margin-right: 8rpx;
  max-width: 120rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.manage-icon {
  width: 24rpx;
  height: 24rpx;
}

.notification {
  position: relative;
}

.notification-icon {
  width: 40rpx;
  height: 40rpx;
}

.notification-badge {
  position: absolute;
  top: -8rpx;
  right: -8rpx;
  background-color: #FF6B6B;
  color: #fff;
  font-size: 20rpx;
  padding: 4rpx 8rpx;
  border-radius: 20rpx;
  min-width: 32rpx;
  text-align: center;
}

/* 统计区域 */
.stats-section {
  margin: -20rpx 30rpx 30rpx;
  background-color: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}

.section-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
}

.title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.date {
  font-size: 24rpx;
  color: #999;
}

.more {
  font-size: 24rpx;
  color: #667eea;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20rpx;
}

.stats-card {
  text-align: center;
  padding: 30rpx 20rpx;
  background-color: #f8f9fa;
  border-radius: 12rpx;
}

.stats-number {
  display: block;
  font-size: 48rpx;
  font-weight: 700;
  color: #333;
  margin-bottom: 8rpx;
}

.stats-number.amount-positive {
  color: #1AAD19;
}

.stats-number.pending {
  color: #FF9500;
}

.stats-label {
  font-size: 24rpx;
  color: #666;
}

/* 待处理事项 */
.pending-section {
  margin: 0 30rpx 30rpx;
  background-color: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.pending-list {
  margin-top: 20rpx;
}

.pending-item {
  display: flex;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.pending-item:last-child {
  border-bottom: none;
}

.pending-icon {
  width: 60rpx;
  height: 60rpx;
  margin-right: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #fff2e8;
  border-radius: 50%;
}

.pending-icon .icon {
  width: 32rpx;
  height: 32rpx;
}

.pending-content {
  flex: 1;
}

.pending-title {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
  margin-bottom: 8rpx;
  display: block;
}

.pending-desc {
  font-size: 24rpx;
  color: #666;
}

.pending-count {
  background-color: #FF6B6B;
  color: #fff;
  font-size: 22rpx;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  min-width: 40rpx;
  text-align: center;
}

/* 快速操作 */
.quick-actions-section {
  margin: 0 30rpx 30rpx;
  background-color: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.quick-actions {
  margin-top: 20rpx;
}

.action-row {
  display: flex;
  gap: 20rpx;
  margin-bottom: 20rpx;
}

.action-row:last-child {
  margin-bottom: 0;
}

.action-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 30rpx 20rpx;
  background-color: #f8f9fa;
  border-radius: 12rpx;
  transition: all 0.3s ease;
}

.action-item:active {
  transform: scale(0.95);
  background-color: #e9ecef;
}

.action-icon {
  width: 60rpx;
  height: 60rpx;
  margin-bottom: 16rpx;
}

.action-text {
  font-size: 24rpx;
  color: #333;
  text-align: center;
}

/* 业绩趋势 */
.performance-section {
  margin: 0 30rpx 30rpx;
  background-color: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.performance-chart {
  margin-top: 20rpx;
}

.chart-placeholder {
  height: 200rpx;
  background-color: #f8f9fa;
  border-radius: 12rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.chart-text {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 16rpx;
}

.chart-summary {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.summary-item {
  font-size: 24rpx;
  color: #999;
}

/* 成员排行 */
.ranking-section {
  margin: 0 30rpx 30rpx;
  background-color: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.ranking-list {
  margin-top: 20rpx;
}

.ranking-item {
  display: flex;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
  position: relative;
}

.ranking-item:last-child {
  border-bottom: none;
}

.ranking-position {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20rpx;
}

.position-number {
  font-size: 32rpx;
  font-weight: 700;
  color: #667eea;
}

.member-info {
  flex: 1;
  display: flex;
  align-items: center;
}

.member-avatar {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  margin-right: 20rpx;
}

.member-details {
  flex: 1;
}

.member-name {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
  margin-bottom: 8rpx;
  display: block;
}

.member-stats {
  font-size: 24rpx;
  color: #666;
}

.ranking-badge {
  position: absolute;
  top: 16rpx;
  right: 0;
}

.badge-icon {
  width: 32rpx;
  height: 32rpx;
}

/* 最新动态 */
.activity-section {
  margin: 0 30rpx 30rpx;
  background-color: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.activity-list {
  margin-top: 20rpx;
}

.activity-item {
  display: flex;
  align-items: flex-start;
  padding: 16rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.activity-item:last-child {
  border-bottom: none;
}

.activity-time {
  font-size: 22rpx;
  color: #999;
  width: 120rpx;
  flex-shrink: 0;
  margin-right: 20rpx;
  margin-top: 4rpx;
}

.activity-content {
  flex: 1;
}

.activity-text {
  font-size: 26rpx;
  color: #333;
  line-height: 1.5;
  margin-bottom: 8rpx;
  display: block;
}

.activity-type {
  font-size: 22rpx;
  color: #667eea;
  background-color: #f0f2ff;
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  display: inline-block;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 60rpx 40rpx;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
}
