// pages/companion/orders/orders.ts
import api from '../../../utils/api'
import { formatTime, getRelativeTime, formatAmount, showError, showSuccess, showConfirm } from '../../../utils/util'

const app = getApp<IAppOption>()

Page({
  data: {
    orders: [] as any[],
    loading: false,
    hasMore: true,
    page: 1,
    pageSize: 20,
    
    // 筛选选项
    statusOptions: [
      { value: '', text: '全部状态' },
      { value: 'pending', text: '待审核' },
      { value: 'approved', text: '已通过' },
      { value: 'rejected', text: '已拒绝' },
      { value: 'in_progress', text: '进行中' },
      { value: 'completed', text: '已完成' },
      { value: 'cancelled', text: '已取消' }
    ],
    selectedStatusIndex: 0,
    
    typeOptions: [
      { value: '', text: '全部类型' },
      { value: 'gaming', text: '游戏单' },
      { value: 'gift', text: '礼物单' },
      { value: 'fine', text: '罚款信息' },
      { value: 'group_fee', text: '团费信息' },
      { value: 'deposit', text: '存单信息' },
      { value: 'predeposit', text: '预存信息' },
      { value: 'client_acquisition', text: '拉老板信息' }
    ],
    selectedTypeIndex: 0,
    
    searchKeyword: '',
    
    // 创建选项
    showCreateModal: false,
    createOptions: [
      {
        type: 'gaming',
        name: '游戏单',
        desc: '记录陪玩游戏订单',
        icon: '/assets/icons/game.png'
      },
      {
        type: 'gift',
        name: '礼物单',
        desc: '记录收到的礼物',
        icon: '/assets/icons/gift.png'
      },
      {
        type: 'deposit',
        name: '存单信息',
        desc: '记录保证金等存款',
        icon: '/assets/icons/deposit.png'
      },
      {
        type: 'predeposit',
        name: '预存信息',
        desc: '记录预存款项',
        icon: '/assets/icons/predeposit.png'
      },
      {
        type: 'client_acquisition',
        name: '拉老板信息',
        desc: '记录客户获取情况',
        icon: '/assets/icons/client.png'
      }
    ]
  },

  onLoad() {
    this.loadOrders()
  },

  onShow() {
    // 刷新数据
    this.refreshOrders()
  },

  onPullDownRefresh() {
    this.refreshOrders().finally(() => {
      wx.stopPullDownRefresh()
    })
  },

  onReachBottom() {
    if (this.data.hasMore && !this.data.loading) {
      this.loadMore()
    }
  },

  // 加载订单列表
  async loadOrders(isRefresh = false) {
    if (this.data.loading) return

    const currentClub = app.globalData.currentClub
    if (!currentClub) {
      showError('请先选择俱乐部')
      return
    }

    this.setData({ loading: true })

    try {
      const page = isRefresh ? 1 : this.data.page
      const filters = this.getFilters()
      
      const res = await api.getOrders(currentClub.id, {
        ...filters,
        page,
        pageSize: this.data.pageSize
      })

      if (res.success) {
        const newOrders = this.processOrders(res.data.orders || [])
        
        this.setData({
          orders: isRefresh ? newOrders : [...this.data.orders, ...newOrders],
          hasMore: newOrders.length === this.data.pageSize,
          page: isRefresh ? 2 : this.data.page + 1
        })
      }
    } catch (error) {
      console.error('加载订单失败:', error)
      showError('加载订单失败')
    } finally {
      this.setData({ loading: false })
    }
  },

  // 刷新订单
  async refreshOrders() {
    this.setData({ 
      orders: [],
      page: 1,
      hasMore: true
    })
    await this.loadOrders(true)
  },

  // 加载更多
  loadMore() {
    this.loadOrders()
  },

  // 获取筛选条件
  getFilters() {
    const filters: any = {}
    
    const selectedStatus = this.data.statusOptions[this.data.selectedStatusIndex]
    if (selectedStatus.value) {
      filters.status = selectedStatus.value
    }
    
    const selectedType = this.data.typeOptions[this.data.selectedTypeIndex]
    if (selectedType.value) {
      filters.orderType = selectedType.value
    }
    
    if (this.data.searchKeyword.trim()) {
      filters.keyword = this.data.searchKeyword.trim()
    }
    
    return filters
  },

  // 处理订单数据
  processOrders(orders: any[]) {
    return orders.map(order => ({
      ...order,
      typeText: this.getOrderTypeText(order.orderType),
      statusText: this.getOrderStatusText(order.status),
      createdAt: getRelativeTime(order.createdAt),
      title: this.getOrderTitle(order),
      description: this.getOrderDescription(order),
      amountText: this.getAmountText(order),
      amountClass: this.getAmountClass(order),
      showActions: ['pending', 'rejected'].includes(order.status)
    }))
  },

  // 状态筛选变化
  onStatusChange(e: any) {
    this.setData({ selectedStatusIndex: e.detail.value })
    this.refreshOrders()
  },

  // 类型筛选变化
  onTypeChange(e: any) {
    this.setData({ selectedTypeIndex: e.detail.value })
    this.refreshOrders()
  },

  // 搜索输入
  onSearchInput(e: any) {
    this.setData({ searchKeyword: e.detail.value })
  },

  // 搜索
  onSearch() {
    this.refreshOrders()
  },

  // 显示创建选项
  showCreateOptions() {
    this.setData({ showCreateModal: true })
  },

  // 隐藏创建选项
  hideCreateOptions() {
    this.setData({ showCreateModal: false })
  },

  // 创建订单
  createOrder(e: any) {
    const type = e.currentTarget.dataset.type
    this.hideCreateOptions()
    
    wx.navigateTo({
      url: `/pages/companion/create-order/create-order?type=${type}`
    })
  },

  // 查看订单详情
  viewOrderDetail(e: any) {
    const id = e.currentTarget.dataset.id
    wx.navigateTo({
      url: `/pages/companion/order-detail/order-detail?id=${id}`
    })
  },

  // 编辑订单
  editOrder(e: any) {
    const id = e.currentTarget.dataset.id
    const order = this.data.orders.find(o => o.id === id)
    if (order) {
      wx.navigateTo({
        url: `/pages/companion/create-order/create-order?type=${order.orderType}&id=${id}`
      })
    }
  },

  // 删除订单
  async deleteOrder(e: any) {
    const id = e.currentTarget.dataset.id
    
    const confirmed = await showConfirm('确定要删除这个订单吗？', '删除确认')
    if (!confirmed) return

    try {
      const res = await api.deleteOrder(id)
      if (res.success) {
        showSuccess('删除成功')
        this.refreshOrders()
      }
    } catch (error) {
      console.error('删除订单失败:', error)
      showError('删除失败')
    }
  },

  // 重新提交订单
  async resubmitOrder(e: any) {
    const id = e.currentTarget.dataset.id
    
    try {
      const res = await api.callFunction('resubmitOrder', { orderId: id })
      if (res.success) {
        showSuccess('重新提交成功')
        this.refreshOrders()
      }
    } catch (error) {
      console.error('重新提交失败:', error)
      showError('重新提交失败')
    }
  },

  // 阻止事件冒泡
  stopPropagation() {},

  // 获取订单类型文本
  getOrderTypeText(type: string) {
    const typeMap: { [key: string]: string } = {
      'gaming': '游戏单',
      'gift': '礼物单',
      'fine': '罚款信息',
      'group_fee': '团费信息',
      'deposit': '存单信息',
      'predeposit': '预存信息',
      'client_acquisition': '拉老板信息'
    }
    return typeMap[type] || '未知类型'
  },

  // 获取订单状态文本
  getOrderStatusText(status: string) {
    const statusMap: { [key: string]: string } = {
      'pending': '待审核',
      'approved': '已通过',
      'rejected': '已拒绝',
      'in_progress': '进行中',
      'completed': '已完成',
      'cancelled': '已取消'
    }
    return statusMap[status] || '未知状态'
  },

  // 获取订单标题
  getOrderTitle(order: any) {
    switch (order.orderType) {
      case 'gaming':
        return order.gameInfo?.gameName || '游戏订单'
      case 'gift':
        return order.giftInfo?.giftName || '礼物订单'
      case 'fine':
        return order.fineInfo?.reason || '罚款记录'
      case 'group_fee':
        return `${order.feeInfo?.period || ''} 团费`
      case 'deposit':
        return order.depositInfo?.purpose || '存款记录'
      case 'predeposit':
        return order.predepositInfo?.purpose || '预存记录'
      case 'client_acquisition':
        return `客户: ${order.clientInfo?.clientName || ''}`
      default:
        return '订单记录'
    }
  },

  // 获取订单描述
  getOrderDescription(order: any) {
    switch (order.orderType) {
      case 'gaming':
        return `${order.gameInfo?.gameMode || ''} | ${order.gameInfo?.duration || 0}分钟`
      case 'gift':
        return `数量: ${order.giftInfo?.quantity || 1} | 单价: ¥${order.giftInfo?.unitPrice || 0}`
      case 'fine':
        return order.fineInfo?.description || ''
      case 'group_fee':
        return order.feeInfo?.description || ''
      case 'deposit':
        return order.depositInfo?.depositType || ''
      case 'predeposit':
        return order.predepositInfo?.gameAccount || ''
      case 'client_acquisition':
        return order.clientInfo?.clientSource || ''
      default:
        return ''
    }
  },

  // 获取金额文本
  getAmountText(order: any) {
    let amount = 0
    
    switch (order.orderType) {
      case 'gaming':
      case 'gift':
        amount = order.financial?.companionEarning || 0
        break
      case 'fine':
        amount = -(order.fineInfo?.amount || 0)
        break
      case 'group_fee':
        amount = -(order.feeInfo?.amount || 0)
        break
      case 'deposit':
        amount = order.depositInfo?.amount || 0
        break
      case 'predeposit':
        amount = order.predepositInfo?.amount || 0
        break
      case 'client_acquisition':
        amount = order.reward?.amount || 0
        break
    }
    
    return formatAmount(Math.abs(amount))
  },

  // 获取金额样式类
  getAmountClass(order: any) {
    switch (order.orderType) {
      case 'gaming':
      case 'gift':
      case 'client_acquisition':
        return 'amount-positive'
      case 'fine':
      case 'group_fee':
        return 'amount-negative'
      default:
        return 'amount-normal'
    }
  }
})
