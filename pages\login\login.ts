// pages/login/login.ts
import api from '../../utils/api'
import { showError, showSuccess } from '../../utils/util'

const app = getApp<IAppOption>()

Page({
  data: {
    userInfo: null as WechatMiniprogram.UserInfo | null,
    selectedRole: '' as 'companion' | 'manager' | '',
    isLogging: false
  },

  onLoad() {
    // 检查是否已经登录
    const userInfo = wx.getStorageSync('userInfo')
    const userRole = wx.getStorageSync('userRole')
    
    if (userInfo && userRole) {
      // 已登录，跳转到对应页面
      this.navigateToHome(userRole)
    }
  },

  // 获取用户信息
  async onGetUserProfile(e: any) {
    if (e.detail.errMsg === 'getUserProfile:ok') {
      this.setData({
        userInfo: e.detail.userInfo
      })
    } else {
      showError('需要授权才能使用')
    }
  },

  // 选择角色
  selectRole(e: any) {
    const role = e.currentTarget.dataset.role
    this.setData({
      selectedRole: role
    })
  },

  // 确认登录
  async confirmLogin() {
    if (!this.data.selectedRole) {
      showError('请选择身份')
      return
    }

    if (!this.data.userInfo) {
      showError('请先授权获取用户信息')
      return
    }

    this.setData({ isLogging: true })

    try {
      // 1. 微信登录获取code
      const loginRes = await this.wxLogin()
      
      // 2. 调用后端登录接口
      const apiRes = await api.login(loginRes.code)
      
      if (apiRes.success) {
        // 3. 保存用户信息和角色
        const userInfo = {
          ...this.data.userInfo,
          openid: apiRes.data.openid,
          role: this.data.selectedRole
        }

        wx.setStorageSync('userInfo', userInfo)
        wx.setStorageSync('userRole', this.data.selectedRole)
        
        // 4. 更新全局数据
        app.globalData.userInfo = userInfo
        app.globalData.userRole = this.data.selectedRole
        app.globalData.openid = apiRes.data.openid

        // 5. 创建或更新用户记录
        await this.createOrUpdateUser(userInfo)

        showSuccess('登录成功')
        
        // 6. 跳转到对应首页
        setTimeout(() => {
          this.navigateToHome(this.data.selectedRole)
        }, 1000)
        
      } else {
        showError(apiRes.message || '登录失败')
      }
    } catch (error: any) {
      console.error('登录失败:', error)
      showError('登录失败，请重试')
    } finally {
      this.setData({ isLogging: false })
    }
  },

  // 微信登录
  wxLogin(): Promise<any> {
    return new Promise((resolve, reject) => {
      wx.login({
        success: resolve,
        fail: reject
      })
    })
  },

  // 创建或更新用户记录
  async createOrUpdateUser(userInfo: any) {
    try {
      await api.callFunction('createOrUpdateUser', {
        userInfo: {
          openid: userInfo.openid,
          nickname: userInfo.nickName,
          avatar: userInfo.avatarUrl,
          role: userInfo.role
        }
      })
    } catch (error) {
      console.error('创建用户记录失败:', error)
    }
  },

  // 跳转到首页
  navigateToHome(role: string) {
    let url = ''
    
    switch (role) {
      case 'companion':
        url = '/pages/companion/home/<USER>'
        break
      case 'manager':
        url = '/pages/manager/home/<USER>'
        break
      default:
        url = '/pages/companion/home/<USER>'
    }

    wx.reLaunch({ url })
  }
})
