/* pages/companion/orders/orders.wxss */

.container {
  background-color: #f8f8f8;
  min-height: 100vh;
}

/* 筛选栏 */
.filter-bar {
  background-color: #fff;
  padding: 20rpx 30rpx;
  display: flex;
  align-items: center;
  gap: 20rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.filter-item {
  flex-shrink: 0;
}

.filter-text {
  display: flex;
  align-items: center;
  padding: 16rpx 20rpx;
  background-color: #f8f9fa;
  border-radius: 8rpx;
  font-size: 26rpx;
  color: #333;
}

.filter-arrow {
  width: 20rpx;
  height: 20rpx;
  margin-left: 8rpx;
}

.search-box {
  flex: 1;
  position: relative;
}

.search-input {
  width: 100%;
  padding: 16rpx 50rpx 16rpx 20rpx;
  background-color: #f8f9fa;
  border-radius: 8rpx;
  font-size: 26rpx;
}

.search-icon {
  position: absolute;
  right: 20rpx;
  top: 50%;
  transform: translateY(-50%);
  width: 24rpx;
  height: 24rpx;
}

/* 新建按钮 */
.create-btn {
  position: fixed;
  right: 30rpx;
  bottom: 120rpx;
  width: 120rpx;
  height: 120rpx;
  background-color: #1AAD19;
  border-radius: 50%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 20rpx rgba(26, 173, 25, 0.3);
  z-index: 100;
}

.create-icon {
  width: 40rpx;
  height: 40rpx;
  margin-bottom: 8rpx;
}

.create-text {
  font-size: 20rpx;
  color: #fff;
}

/* 订单列表 */
.order-list {
  padding: 20rpx 30rpx;
}

.order-item {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.order-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.order-type-badge {
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 22rpx;
  color: #fff;
}

.type-gaming {
  background-color: #1AAD19;
}

.type-gift {
  background-color: #FF6B6B;
}

.type-fine {
  background-color: #FF9500;
}

.type-group_fee {
  background-color: #4ECDC4;
}

.type-deposit {
  background-color: #9B59B6;
}

.type-predeposit {
  background-color: #3498DB;
}

.type-client_acquisition {
  background-color: #E67E22;
}

.order-id {
  font-size: 24rpx;
  color: #999;
}

.order-status {
  padding: 6rpx 12rpx;
  border-radius: 12rpx;
  font-size: 20rpx;
  color: #fff;
}

.status-pending {
  background-color: #FF9500;
}

.status-approved {
  background-color: #1AAD19;
}

.status-rejected {
  background-color: #FF6B6B;
}

.status-in_progress {
  background-color: #4ECDC4;
}

.status-completed {
  background-color: #1AAD19;
}

.status-cancelled {
  background-color: #999;
}

.order-content {
  margin-bottom: 20rpx;
}

.order-title {
  font-size: 30rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 8rpx;
  display: block;
}

.order-desc {
  font-size: 26rpx;
  color: #666;
  display: block;
}

.order-details {
  margin-bottom: 20rpx;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8rpx;
}

.detail-label {
  font-size: 24rpx;
  color: #999;
}

.detail-value {
  font-size: 24rpx;
  color: #333;
}

.order-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 20rpx;
  border-top: 1rpx solid #f0f0f0;
}

.order-time {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.time-label {
  font-size: 22rpx;
  color: #999;
}

.time-value {
  font-size: 22rpx;
  color: #666;
}

.order-amount {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.amount-label {
  font-size: 22rpx;
  color: #999;
}

.amount-value {
  font-size: 28rpx;
  font-weight: 600;
}

.amount-positive {
  color: #1AAD19;
}

.amount-negative {
  color: #FF6B6B;
}

.amount-normal {
  color: #333;
}

.order-actions {
  display: flex;
  gap: 16rpx;
  margin-top: 20rpx;
  padding-top: 20rpx;
  border-top: 1rpx solid #f0f0f0;
}

.action-btn {
  flex: 1;
  height: 60rpx;
  border-radius: 8rpx;
  font-size: 24rpx;
  border: none;
}

.btn-primary {
  background-color: #1AAD19;
  color: #fff;
}

.btn-secondary {
  background-color: #f0f0f0;
  color: #333;
}

.btn-danger {
  background-color: #FF6B6B;
  color: #fff;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 100rpx 40rpx;
}

.empty-icon {
  width: 120rpx;
  height: 120rpx;
  margin-bottom: 30rpx;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
  margin-bottom: 40rpx;
}

.empty-btn {
  background-color: #1AAD19;
  color: #fff;
  border-radius: 12rpx;
  padding: 20rpx 40rpx;
  font-size: 28rpx;
  border: none;
}

/* 加载更多 */
.load-more {
  padding: 40rpx;
  text-align: center;
}

.load-more-btn {
  background-color: #f8f9fa;
  color: #666;
  border: none;
  border-radius: 8rpx;
  padding: 20rpx 40rpx;
  font-size: 26rpx;
}

/* 创建选项弹窗 */
.create-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: flex-end;
  z-index: 1000;
}

.modal-content {
  background-color: #fff;
  border-radius: 24rpx 24rpx 0 0;
  width: 100%;
  max-height: 80vh;
  overflow: hidden;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.modal-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.close-icon {
  width: 40rpx;
  height: 40rpx;
}

.create-options {
  padding: 20rpx 0;
}

.create-option {
  display: flex;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.create-option:last-child {
  border-bottom: none;
}

.option-icon {
  width: 60rpx;
  height: 60rpx;
  margin-right: 30rpx;
}

.option-info {
  flex: 1;
}

.option-name {
  font-size: 30rpx;
  color: #333;
  font-weight: 500;
  margin-bottom: 8rpx;
  display: block;
}

.option-desc {
  font-size: 24rpx;
  color: #999;
}

.option-arrow {
  width: 32rpx;
  height: 32rpx;
}
