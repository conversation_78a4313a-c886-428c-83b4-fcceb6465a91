# 陪玩服务管理系统

一个基于微信小程序的陪玩服务报单管理系统，支持多俱乐部管理和陪玩师服务报告。

## 🚀 功能特性

### 陪玩师功能
- ✅ 微信授权登录
- 📱 多俱乐部切换
- 📋 七种报单类型：游戏单、礼物单、罚款信息、团费信息、存单信息、预存信息、拉老板信息
- 📊 收益统计和趋势分析
- 📢 公告查看和通知

### 管理员功能
- 🏢 俱乐部创建和管理
- 👥 成员邀请和权限管理
- ✅ 报单审核和处理
- 💰 财务管理和结算
- 📊 统计报表和数据分析
- 📢 公告发布和管理

### 系统特性
- 🔐 基于角色的权限控制
- 🔄 实时数据同步
- 📱 自动更新检测
- 💾 数据备份和安全
- 📈 性能优化

## 🛠️ 技术栈

- **前端**: 微信小程序原生开发 + TypeScript
- **后端**: 微信云开发 (Cloud Base)
- **数据库**: 云数据库 (NoSQL)
- **存储**: 云存储
- **函数**: 云函数

## 📦 项目结构

```
companion-gaming-system/
├── pages/                    # 页面文件
│   ├── login/               # 登录页面
│   ├── companion/           # 陪玩师端页面
│   │   ├── home/           # 首页
│   │   ├── orders/         # 报单管理
│   │   ├── create-order/   # 创建报单
│   │   ├── earnings/       # 收益统计
│   │   └── profile/        # 个人中心
│   └── manager/            # 管理员端页面
│       ├── home/           # 管理首页
│       ├── club-manage/    # 俱乐部管理
│       ├── member-manage/  # 成员管理
│       ├── order-review/   # 报单审核
│       ├── financial/      # 财务管理
│       └── statistics/     # 统计报表
├── cloudfunctions/          # 云函数
│   ├── login/              # 用户登录
│   ├── createOrUpdateUser/ # 用户管理
│   ├── getUserClubs/       # 获取用户俱乐部
│   ├── createGameOrder/    # 创建游戏单
│   └── getOrders/          # 获取订单列表
├── utils/                   # 工具类
│   ├── util.ts             # 通用工具函数
│   └── api.ts              # API服务封装
├── database/               # 数据库配置
│   └── database.rules.json # 数据库权限规则
├── docs/                   # 文档
│   ├── system-architecture.md
│   ├── database-schema.md
│   ├── ui-design.md
│   └── implementation-plan.md
├── app.json               # 小程序配置
├── app.ts                 # 全局逻辑
├── app.wxss              # 全局样式
└── project.config.json   # 项目配置
```

## 🚀 快速开始

### 1. 环境准备

1. 安装微信开发者工具
2. 注册微信小程序账号
3. 开通云开发服务

### 2. 项目配置

1. **克隆项目**
   ```bash
   git clone <repository-url>
   cd companion-gaming-system
   ```

2. **配置小程序AppID**
   - 在 `project.config.json` 中修改 `appid` 为你的小程序AppID

3. **配置云开发环境**
   - 在微信开发者工具中创建云开发环境
   - 修改 `app.ts` 中的环境ID：
   ```typescript
   wx.cloud.init({
     env: 'your-cloud-env-id', // 替换为你的云开发环境ID
     traceUser: true,
   })
   ```

### 3. 数据库初始化

1. **创建数据库集合**
   在云开发控制台创建以下集合：
   - `users` - 用户信息
   - `clubs` - 俱乐部信息
   - `club_members` - 俱乐部成员
   - `game_orders` - 游戏订单
   - `gift_orders` - 礼物订单
   - `fine_records` - 罚款记录
   - `group_fee_records` - 团费记录
   - `deposit_records` - 存单记录
   - `predeposit_records` - 预存记录
   - `client_acquisition_records` - 客户获取记录
   - `announcements` - 公告
   - `financial_settlements` - 财务结算

2. **配置数据库权限**
   - 将 `database/database.rules.json` 中的权限规则应用到云数据库

### 4. 云函数部署

1. **安装依赖**
   ```bash
   cd cloudfunctions/login
   npm install
   cd ../createOrUpdateUser
   npm install
   # ... 为每个云函数安装依赖
   ```

2. **部署云函数**
   - 在微信开发者工具中右键每个云函数文件夹
   - 选择"上传并部署：云端安装依赖"

### 5. 运行项目

1. 在微信开发者工具中打开项目
2. 点击"编译"按钮
3. 在模拟器中测试功能

## 📱 使用指南

### 陪玩师使用流程

1. **登录注册**
   - 微信授权登录
   - 选择"陪玩师"角色

2. **加入俱乐部**
   - 通过邀请码加入俱乐部
   - 或等待管理员邀请

3. **提交报单**
   - 选择报单类型（游戏单、礼物单等）
   - 填写详细信息
   - 提交审核

4. **查看收益**
   - 查看收益统计
   - 查看历史记录

### 管理员使用流程

1. **登录注册**
   - 微信授权登录
   - 选择"俱乐部管理员"角色

2. **创建俱乐部**
   - 填写俱乐部信息
   - 设置佣金比例等参数

3. **管理成员**
   - 邀请陪玩师加入
   - 设置成员权限

4. **审核报单**
   - 查看待审核报单
   - 审核通过或拒绝

5. **财务管理**
   - 查看收益统计
   - 进行结算操作

## 🔧 开发指南

### 添加新的报单类型

1. **数据库设计**
   - 在 `docs/database-schema.md` 中添加新的集合设计
   - 创建对应的数据库集合

2. **云函数开发**
   - 创建对应的云函数处理逻辑
   - 参考 `cloudfunctions/createGameOrder/` 的实现

3. **前端页面**
   - 在 `pages/companion/create-order/` 中添加表单
   - 更新 `utils/api.ts` 中的API调用

### 自定义样式

- 修改 `app.wxss` 中的全局样式
- 各页面的样式在对应的 `.wxss` 文件中

### 权限控制

- 数据库权限在 `database/database.rules.json` 中配置
- 业务逻辑权限在云函数中实现

## 🚀 部署上线

### 1. 测试验证

- 完整测试所有功能
- 检查数据安全性
- 性能优化

### 2. 小程序审核

- 完善小程序信息
- 提交微信审核
- 等待审核通过

### 3. 正式发布

- 发布小程序版本
- 配置生产环境
- 监控运行状态

## 📞 技术支持

如有问题，请查看：
- [系统架构文档](docs/system-architecture.md)
- [数据库设计文档](docs/database-schema.md)
- [UI设计文档](docs/ui-design.md)
- [实施计划文档](docs/implementation-plan.md)

## 📄 许可证

MIT License

## 🤝 贡献

欢迎提交 Issue 和 Pull Request！
