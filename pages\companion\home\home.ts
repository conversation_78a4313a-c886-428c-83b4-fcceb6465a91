// pages/companion/home/<USER>
import api from '../../../utils/api'
import { formatDate, formatAmount, getRelativeTime, showError, showSuccess } from '../../../utils/util'

const app = getApp<IAppOption>()

Page({
  data: {
    userInfo: null as any,
    currentClub: null as any,
    clubs: [] as any[],
    todayDate: '',
    todayStats: {
      gameOrders: 0,
      giftOrders: 0,
      totalEarnings: '0.00',
      pendingAmount: '0.00'
    },
    announcements: [] as any[],
    recentOrders: [] as any[],
    weeklyEarnings: '0.00',
    unreadCount: 0,
    showClubSwitchModal: false
  },

  onLoad() {
    this.initPage()
  },

  onShow() {
    this.refreshData()
  },

  onPullDownRefresh() {
    this.refreshData().finally(() => {
      wx.stopPullDownRefresh()
    })
  },

  // 初始化页面
  async initPage() {
    // 检查登录状态
    const userInfo = wx.getStorageSync('userInfo')
    const userRole = wx.getStorageSync('userRole')
    
    if (!userInfo || userRole !== 'companion') {
      wx.reLaunch({
        url: '/pages/login/login'
      })
      return
    }

    this.setData({
      userInfo,
      todayDate: formatDate(new Date())
    })

    // 加载数据
    await this.loadUserClubs()
    await this.refreshData()
  },

  // 加载用户俱乐部
  async loadUserClubs() {
    try {
      const res = await api.getUserClubs()
      if (res.success) {
        const clubs = res.data.clubs || []
        this.setData({ clubs })
        
        // 设置当前俱乐部
        if (clubs.length > 0) {
          const savedClub = wx.getStorageSync('currentClub')
          const currentClub = savedClub && clubs.find((c: any) => c.id === savedClub.id) || clubs[0]
          this.setData({ currentClub })
          app.globalData.currentClub = currentClub
        }
      }
    } catch (error) {
      console.error('加载俱乐部失败:', error)
    }
  },

  // 刷新数据
  async refreshData() {
    if (!this.data.currentClub) return

    await Promise.all([
      this.loadTodayStats(),
      this.loadAnnouncements(),
      this.loadRecentOrders(),
      this.loadWeeklyEarnings()
    ])
  },

  // 加载今日统计
  async loadTodayStats() {
    try {
      const res = await api.callFunction('getTodayStats', {
        clubId: this.data.currentClub.id
      }, false)
      
      if (res.success) {
        this.setData({
          todayStats: {
            gameOrders: res.data.gameOrders || 0,
            giftOrders: res.data.giftOrders || 0,
            totalEarnings: formatAmount(res.data.totalEarnings || 0, false),
            pendingAmount: formatAmount(res.data.pendingAmount || 0, false)
          }
        })
      }
    } catch (error) {
      console.error('加载今日统计失败:', error)
    }
  },

  // 加载公告
  async loadAnnouncements() {
    try {
      const res = await api.getAnnouncements(this.data.currentClub.id)
      if (res.success) {
        const announcements = (res.data.announcements || []).slice(0, 3).map((item: any) => ({
          ...item,
          publishedAt: getRelativeTime(item.publishedAt)
        }))
        
        this.setData({ 
          announcements,
          unreadCount: announcements.filter((a: any) => !a.isRead).length
        })
      }
    } catch (error) {
      console.error('加载公告失败:', error)
    }
  },

  // 加载最近订单
  async loadRecentOrders() {
    try {
      const res = await api.getOrders(this.data.currentClub.id, {
        limit: 5,
        orderBy: 'createdAt',
        order: 'desc'
      })
      
      if (res.success) {
        const recentOrders = (res.data.orders || []).map((item: any) => ({
          ...item,
          typeText: this.getOrderTypeText(item.orderType),
          statusText: this.getOrderStatusText(item.status),
          createdAt: getRelativeTime(item.createdAt),
          amount: formatAmount(item.financial?.companionEarning || 0, false),
          description: this.getOrderDescription(item)
        }))
        
        this.setData({ recentOrders })
      }
    } catch (error) {
      console.error('加载最近订单失败:', error)
    }
  },

  // 加载本周收益
  async loadWeeklyEarnings() {
    try {
      const res = await api.callFunction('getWeeklyEarnings', {
        clubId: this.data.currentClub.id
      }, false)
      
      if (res.success) {
        this.setData({
          weeklyEarnings: formatAmount(res.data.weeklyEarnings || 0, false)
        })
      }
    } catch (error) {
      console.error('加载本周收益失败:', error)
    }
  },

  // 显示俱乐部切换
  showClubSwitch() {
    if (this.data.clubs.length <= 1) {
      showError('您只加入了一个俱乐部')
      return
    }
    this.setData({ showClubSwitchModal: true })
  },

  // 隐藏俱乐部切换
  hideClubSwitch() {
    this.setData({ showClubSwitchModal: false })
  },

  // 切换俱乐部
  async switchClub(e: any) {
    const club = e.currentTarget.dataset.club
    if (club.id === this.data.currentClub.id) {
      this.hideClubSwitch()
      return
    }

    this.setData({ 
      currentClub: club,
      showClubSwitchModal: false
    })
    
    wx.setStorageSync('currentClub', club)
    app.globalData.currentClub = club
    
    showSuccess(`已切换到 ${club.name}`)
    
    // 刷新数据
    await this.refreshData()
  },

  // 阻止事件冒泡
  stopPropagation() {},

  // 显示通知
  showNotifications() {
    wx.navigateTo({
      url: '/pages/companion/notifications/notifications'
    })
  },

  // 查看所有公告
  viewAllAnnouncements() {
    wx.navigateTo({
      url: '/pages/companion/announcements/announcements'
    })
  },

  // 查看公告详情
  viewAnnouncement(e: any) {
    const id = e.currentTarget.dataset.id
    wx.navigateTo({
      url: `/pages/companion/announcement-detail/announcement-detail?id=${id}`
    })
  },

  // 创建游戏单
  createGameOrder() {
    wx.navigateTo({
      url: '/pages/companion/create-order/create-order?type=gaming'
    })
  },

  // 创建礼物单
  createGiftOrder() {
    wx.navigateTo({
      url: '/pages/companion/create-order/create-order?type=gift'
    })
  },

  // 创建存单记录
  createDepositRecord() {
    wx.navigateTo({
      url: '/pages/companion/create-order/create-order?type=deposit'
    })
  },

  // 创建客户记录
  createClientRecord() {
    wx.navigateTo({
      url: '/pages/companion/create-order/create-order?type=client_acquisition'
    })
  },

  // 查看收益详情
  viewEarningsDetail() {
    wx.switchTab({
      url: '/pages/companion/earnings/earnings'
    })
  },

  // 查看所有订单
  viewAllOrders() {
    wx.switchTab({
      url: '/pages/companion/orders/orders'
    })
  },

  // 查看订单详情
  viewOrderDetail(e: any) {
    const id = e.currentTarget.dataset.id
    wx.navigateTo({
      url: `/pages/companion/order-detail/order-detail?id=${id}`
    })
  },

  // 获取订单类型文本
  getOrderTypeText(type: string) {
    const typeMap: { [key: string]: string } = {
      'gaming': '游戏单',
      'gift': '礼物单',
      'fine': '罚款信息',
      'group_fee': '团费信息',
      'deposit': '存单信息',
      'predeposit': '预存信息',
      'client_acquisition': '拉老板信息'
    }
    return typeMap[type] || '未知类型'
  },

  // 获取订单状态文本
  getOrderStatusText(status: string) {
    const statusMap: { [key: string]: string } = {
      'pending': '待审核',
      'approved': '已通过',
      'rejected': '已拒绝',
      'in_progress': '进行中',
      'completed': '已完成',
      'cancelled': '已取消'
    }
    return statusMap[status] || '未知状态'
  },

  // 获取订单描述
  getOrderDescription(order: any) {
    switch (order.orderType) {
      case 'gaming':
        return `${order.gameInfo?.gameName || ''} | ${order.gameInfo?.duration || 0}分钟`
      case 'gift':
        return `${order.giftInfo?.giftName || ''} x${order.giftInfo?.quantity || 1}`
      case 'fine':
        return order.fineInfo?.reason || '罚款'
      case 'group_fee':
        return `${order.feeInfo?.period || ''} 团费`
      case 'deposit':
        return `${order.depositInfo?.purpose || ''} 存款`
      case 'predeposit':
        return `${order.predepositInfo?.purpose || ''} 预存`
      case 'client_acquisition':
        return `客户: ${order.clientInfo?.clientName || ''}`
      default:
        return '订单信息'
    }
  }
})
