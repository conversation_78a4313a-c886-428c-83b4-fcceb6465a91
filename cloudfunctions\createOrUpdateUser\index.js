// 云函数入口文件
const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()

// 云函数入口函数
exports.main = async (event, context) => {
  const wxContext = cloud.getWXContext()
  
  try {
    const { userInfo } = event
    const openid = wxContext.OPENID
    
    // 检查用户是否已存在
    const userQuery = await db.collection('users').where({
      openid: openid
    }).get()
    
    const userData = {
      openid: openid,
      nickname: userInfo.nickname,
      avatar: userInfo.avatar,
      role: userInfo.role,
      status: 'active',
      updatedAt: new Date()
    }
    
    if (userQuery.data.length > 0) {
      // 用户已存在，更新信息
      const userId = userQuery.data[0]._id
      await db.collection('users').doc(userId).update({
        data: userData
      })
      
      return {
        success: true,
        data: {
          userId: userId,
          isNewUser: false
        },
        message: '用户信息更新成功'
      }
    } else {
      // 新用户，创建记录
      userData.createdAt = new Date()
      userData.settings = {
        maxClubs: 5,
        notifications: true,
        autoUpdate: true
      }
      
      const result = await db.collection('users').add({
        data: userData
      })
      
      return {
        success: true,
        data: {
          userId: result._id,
          isNewUser: true
        },
        message: '用户创建成功'
      }
    }
    
  } catch (error) {
    console.error('创建/更新用户失败:', error)
    return {
      success: false,
      message: '操作失败: ' + error.message
    }
  }
}
