// 云函数入口文件
const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()

// 云函数入口函数
exports.main = async (event, context) => {
  const wxContext = cloud.getWXContext()
  
  try {
    const openid = wxContext.OPENID
    
    // 获取用户信息
    const userQuery = await db.collection('users').where({
      openid: openid
    }).get()
    
    if (userQuery.data.length === 0) {
      return {
        success: false,
        message: '用户不存在'
      }
    }
    
    const user = userQuery.data[0]
    
    // 获取用户加入的俱乐部
    const memberQuery = await db.collection('club_members').where({
      userId: user._id,
      status: 'active'
    }).get()
    
    const clubIds = memberQuery.data.map(member => member.clubId)
    
    if (clubIds.length === 0) {
      return {
        success: true,
        data: {
          clubs: [],
          user: user
        },
        message: '获取成功'
      }
    }
    
    // 获取俱乐部详细信息
    const clubsQuery = await db.collection('clubs').where({
      _id: db.command.in(clubIds),
      status: 'active'
    }).get()
    
    // 为每个俱乐部添加成员角色信息
    const clubs = clubsQuery.data.map(club => {
      const membership = memberQuery.data.find(member => member.clubId === club._id)
      return {
        ...club,
        memberRole: membership ? membership.role : 'member',
        joinedAt: membership ? membership.joinedAt : null
      }
    })
    
    return {
      success: true,
      data: {
        clubs: clubs,
        user: user
      },
      message: '获取成功'
    }
    
  } catch (error) {
    console.error('获取用户俱乐部失败:', error)
    return {
      success: false,
      message: '获取失败: ' + error.message
    }
  }
}
