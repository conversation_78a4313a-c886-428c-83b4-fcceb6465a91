/* pages/login/login.wxss */
.container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40rpx;
}

.login-container {
  width: 100%;
  max-width: 600rpx;
  background-color: #fff;
  border-radius: 24rpx;
  padding: 60rpx 40rpx;
  box-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.1);
}

/* Logo区域 */
.logo-section {
  text-align: center;
  margin-bottom: 60rpx;
}

.logo {
  width: 120rpx;
  height: 120rpx;
  margin-bottom: 20rpx;
}

.app-name {
  display: block;
  font-size: 36rpx;
  font-weight: 700;
  color: #333;
  margin-bottom: 12rpx;
}

.app-desc {
  display: block;
  font-size: 24rpx;
  color: #999;
}

/* 登录表单 */
.login-form {
  margin-bottom: 40rpx;
}

.welcome-text {
  text-align: center;
  margin-bottom: 40rpx;
}

.welcome-title {
  display: block;
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 12rpx;
}

.welcome-desc {
  display: block;
  font-size: 26rpx;
  color: #666;
}

/* 微信登录按钮 */
.login-btn {
  width: 100%;
  height: 88rpx;
  background-color: #1AAD19;
  color: #fff;
  border: none;
  border-radius: 12rpx;
  font-size: 32rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 30rpx;
}

.wechat-icon {
  width: 40rpx;
  height: 40rpx;
  margin-right: 16rpx;
}

/* 用户信息 */
.user-info {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 30rpx;
  background-color: #f8f8f8;
  border-radius: 12rpx;
  margin-bottom: 30rpx;
}

.avatar {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  margin-right: 20rpx;
}

.nickname {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

/* 角色选择 */
.role-selection {
  margin-top: 30rpx;
}

.role-title {
  display: block;
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
  margin-bottom: 30rpx;
  text-align: center;
}

.role-options {
  display: flex;
  gap: 20rpx;
  margin-bottom: 40rpx;
}

.role-option {
  flex: 1;
  padding: 30rpx 20rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 12rpx;
  text-align: center;
  transition: all 0.3s ease;
}

.role-option.selected {
  border-color: #1AAD19;
  background-color: #f0f9f0;
}

.role-icon {
  width: 60rpx;
  height: 60rpx;
  margin-bottom: 16rpx;
}

.role-name {
  display: block;
  font-size: 26rpx;
  color: #333;
  font-weight: 500;
  margin-bottom: 8rpx;
}

.role-desc {
  display: block;
  font-size: 22rpx;
  color: #999;
  line-height: 1.4;
}

/* 确认登录按钮 */
.confirm-btn {
  width: 100%;
  height: 88rpx;
  background-color: #e0e0e0;
  color: #999;
  border: none;
  border-radius: 12rpx;
  font-size: 32rpx;
  transition: all 0.3s ease;
}

.confirm-btn.active {
  background-color: #1AAD19;
  color: #fff;
}

.confirm-btn[disabled] {
  opacity: 0.6;
}

/* 加载状态 */
.loading-section {
  text-align: center;
  padding: 40rpx;
}

.loading-spinner {
  width: 40rpx;
  height: 40rpx;
  border: 4rpx solid #f3f3f3;
  border-top: 4rpx solid #1AAD19;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 20rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 26rpx;
  color: #666;
}

/* 底部信息 */
.footer {
  text-align: center;
  padding-top: 30rpx;
  border-top: 1rpx solid #f0f0f0;
}

.version {
  display: block;
  font-size: 22rpx;
  color: #999;
  margin-bottom: 8rpx;
}

.copyright {
  display: block;
  font-size: 22rpx;
  color: #999;
}
