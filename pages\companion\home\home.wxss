/* pages/companion/home/<USER>/

.container {
  background-color: #f8f8f8;
  min-height: 100vh;
}

/* 头部区域 */
.header {
  background: linear-gradient(135deg, #1AAD19 0%, #16a085 100%);
  padding: 20rpx 30rpx 40rpx;
  color: #fff;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.user-info {
  display: flex;
  align-items: center;
}

.avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  margin-right: 20rpx;
  border: 3rpx solid rgba(255, 255, 255, 0.3);
}

.user-details {
  display: flex;
  flex-direction: column;
}

.nickname {
  font-size: 32rpx;
  font-weight: 600;
  margin-bottom: 8rpx;
}

.role {
  font-size: 24rpx;
  opacity: 0.8;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.club-switch {
  display: flex;
  align-items: center;
  background-color: rgba(255, 255, 255, 0.2);
  padding: 12rpx 20rpx;
  border-radius: 20rpx;
}

.club-name {
  font-size: 24rpx;
  margin-right: 8rpx;
  max-width: 120rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.switch-icon {
  width: 24rpx;
  height: 24rpx;
}

.notification {
  position: relative;
}

.notification-icon {
  width: 40rpx;
  height: 40rpx;
}

.notification-badge {
  position: absolute;
  top: -8rpx;
  right: -8rpx;
  background-color: #FF6B6B;
  color: #fff;
  font-size: 20rpx;
  padding: 4rpx 8rpx;
  border-radius: 20rpx;
  min-width: 32rpx;
  text-align: center;
}

/* 统计区域 */
.stats-section {
  margin: -20rpx 30rpx 30rpx;
  background-color: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}

.section-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
}

.title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.date {
  font-size: 24rpx;
  color: #999;
}

.more {
  font-size: 24rpx;
  color: #1AAD19;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20rpx;
}

.stats-card {
  text-align: center;
  padding: 30rpx 20rpx;
  background-color: #f8f9fa;
  border-radius: 12rpx;
}

.stats-number {
  display: block;
  font-size: 48rpx;
  font-weight: 700;
  color: #333;
  margin-bottom: 8rpx;
}

.stats-number.amount-positive {
  color: #1AAD19;
}

.stats-label {
  font-size: 24rpx;
  color: #666;
}

/* 公告区域 */
.announcement-section {
  margin: 0 30rpx 30rpx;
  background-color: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.announcement-list {
  margin-top: 20rpx;
}

.announcement-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
  position: relative;
}

.announcement-item:last-child {
  border-bottom: none;
}

.announcement-content {
  flex: 1;
}

.announcement-title {
  display: block;
  font-size: 28rpx;
  color: #333;
  margin-bottom: 8rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.announcement-time {
  font-size: 22rpx;
  color: #999;
}

.announcement-badge {
  width: 12rpx;
  height: 12rpx;
  background-color: #FF6B6B;
  border-radius: 50%;
  margin-left: 20rpx;
}

/* 快速操作区域 */
.quick-actions-section {
  margin: 0 30rpx 30rpx;
  background-color: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.quick-actions {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20rpx;
  margin-top: 20rpx;
}

.action-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 30rpx 20rpx;
  background-color: #f8f9fa;
  border-radius: 12rpx;
  transition: all 0.3s ease;
}

.action-item:active {
  transform: scale(0.95);
  background-color: #e9ecef;
}

.action-icon {
  width: 60rpx;
  height: 60rpx;
  margin-bottom: 16rpx;
}

.action-text {
  font-size: 24rpx;
  color: #333;
  text-align: center;
}

/* 收益趋势区域 */
.earnings-trend-section {
  margin: 0 30rpx 30rpx;
  background-color: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.trend-chart {
  margin-top: 20rpx;
}

.chart-placeholder {
  height: 200rpx;
  background-color: #f8f9fa;
  border-radius: 12rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.chart-text {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 12rpx;
}

.chart-desc {
  font-size: 24rpx;
  color: #999;
}

/* 最近订单区域 */
.recent-orders-section {
  margin: 0 30rpx 30rpx;
  background-color: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.order-list {
  margin-top: 20rpx;
}

.order-item {
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.order-item:last-child {
  border-bottom: none;
}

.order-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12rpx;
}

.order-type {
  font-size: 26rpx;
  color: #333;
  font-weight: 500;
}

.order-status {
  font-size: 22rpx;
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  color: #fff;
}

.status-pending {
  background-color: #FF9500;
}

.status-approved {
  background-color: #1AAD19;
}

.status-rejected {
  background-color: #FF6B6B;
}

.status-in_progress {
  background-color: #4ECDC4;
}

.status-completed {
  background-color: #1AAD19;
}

.order-desc {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 12rpx;
  display: block;
}

.order-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.order-time {
  font-size: 22rpx;
  color: #999;
}

.order-amount {
  font-size: 26rpx;
  font-weight: 600;
  color: #1AAD19;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 60rpx 40rpx;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
}

/* 俱乐部切换弹窗 */
.club-switch-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  background-color: #fff;
  border-radius: 16rpx;
  width: 600rpx;
  max-height: 80vh;
  overflow: hidden;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.modal-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.close-icon {
  width: 40rpx;
  height: 40rpx;
}

.club-list {
  max-height: 60vh;
  overflow-y: auto;
}

.club-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.club-item:last-child {
  border-bottom: none;
}

.club-item.selected {
  background-color: #f0f9f0;
}

.club-info {
  flex: 1;
}

.club-name {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
  margin-bottom: 8rpx;
  display: block;
}

.club-desc {
  font-size: 24rpx;
  color: #666;
}

.check-icon {
  width: 32rpx;
  height: 32rpx;
  margin-left: 20rpx;
}
