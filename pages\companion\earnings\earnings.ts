// pages/companion/earnings/earnings.ts
import api from '../../../utils/api'
import { formatDate, formatAmount, getRelativeTime, showError } from '../../../utils/util'

const app = getApp<IAppOption>()

Page({
  data: {
    // 时间筛选选项
    periodOptions: [
      { value: 'today', text: '今日' },
      { value: 'week', text: '本周' },
      { value: 'month', text: '本月' },
      { value: 'quarter', text: '本季度' },
      { value: 'year', text: '本年' },
      { value: 'custom', text: '自定义' }
    ],
    selectedPeriodIndex: 2, // 默认选择本月
    showCustomDate: false,
    customStartDate: '',
    customEndDate: '',
    
    // 收益数据
    earningsSummary: {
      netEarnings: '0.00',
      totalRevenue: '0.00',
      gameEarnings: '0.00',
      giftEarnings: '0.00',
      otherEarnings: '0.00',
      totalFines: '0.00',
      totalFees: '0.00'
    },
    
    // 趋势数据
    trendData: {
      maxDaily: '0.00',
      avgDaily: '0.00'
    },
    
    // 构成数据
    compositionData: {
      gamePercentage: 0,
      giftPercentage: 0,
      otherPercentage: 0
    },
    
    // 排名信息
    rankingInfo: {
      clubRank: 0,
      totalMembers: 0,
      completedOrders: 0,
      satisfaction: 0
    },
    
    // 收益记录
    earningsRecords: [] as any[]
  },

  onLoad() {
    this.initCustomDate()
    this.loadEarningsData()
  },

  onShow() {
    this.loadEarningsData()
  },

  onPullDownRefresh() {
    this.loadEarningsData().finally(() => {
      wx.stopPullDownRefresh()
    })
  },

  // 初始化自定义日期
  initCustomDate() {
    const today = new Date()
    const startOfMonth = new Date(today.getFullYear(), today.getMonth(), 1)
    
    this.setData({
      customStartDate: formatDate(startOfMonth),
      customEndDate: formatDate(today)
    })
  },

  // 时间筛选变化
  onPeriodChange(e: any) {
    const index = e.detail.value
    const period = this.data.periodOptions[index]
    
    this.setData({
      selectedPeriodIndex: index,
      showCustomDate: period.value === 'custom'
    })
    
    this.loadEarningsData()
  },

  // 开始日期变化
  onStartDateChange(e: any) {
    this.setData({ customStartDate: e.detail.value })
    if (this.data.showCustomDate) {
      this.loadEarningsData()
    }
  },

  // 结束日期变化
  onEndDateChange(e: any) {
    this.setData({ customEndDate: e.detail.value })
    if (this.data.showCustomDate) {
      this.loadEarningsData()
    }
  },

  // 加载收益数据
  async loadEarningsData() {
    const currentClub = app.globalData.currentClub
    if (!currentClub) {
      showError('请先选择俱乐部')
      return
    }

    try {
      const period = this.getPeriodParams()
      
      // 并行加载各种数据
      await Promise.all([
        this.loadEarningsSummary(currentClub.id, period),
        this.loadTrendData(currentClub.id, period),
        this.loadCompositionData(currentClub.id, period),
        this.loadRankingInfo(currentClub.id, period),
        this.loadEarningsRecords(currentClub.id, period)
      ])
    } catch (error) {
      console.error('加载收益数据失败:', error)
      showError('加载数据失败')
    }
  },

  // 获取时间参数
  getPeriodParams() {
    const selectedPeriod = this.data.periodOptions[this.data.selectedPeriodIndex]
    
    if (selectedPeriod.value === 'custom') {
      return {
        type: 'custom',
        startDate: this.data.customStartDate,
        endDate: this.data.customEndDate
      }
    }
    
    return {
      type: selectedPeriod.value
    }
  },

  // 加载收益汇总
  async loadEarningsSummary(clubId: string, period: any) {
    try {
      const res = await api.callFunction('getEarningsSummary', {
        clubId,
        period
      }, false)
      
      if (res.success) {
        const summary = res.data.summary
        this.setData({
          earningsSummary: {
            netEarnings: formatAmount(summary.netEarnings || 0, false),
            totalRevenue: formatAmount(summary.totalRevenue || 0, false),
            gameEarnings: formatAmount(summary.gameEarnings || 0, false),
            giftEarnings: formatAmount(summary.giftEarnings || 0, false),
            otherEarnings: formatAmount(summary.otherEarnings || 0, false),
            totalFines: formatAmount(summary.totalFines || 0, false),
            totalFees: formatAmount(summary.totalFees || 0, false)
          }
        })
      }
    } catch (error) {
      console.error('加载收益汇总失败:', error)
    }
  },

  // 加载趋势数据
  async loadTrendData(clubId: string, period: any) {
    try {
      const res = await api.callFunction('getEarningsTrend', {
        clubId,
        period
      }, false)
      
      if (res.success) {
        const trend = res.data.trend
        this.setData({
          trendData: {
            maxDaily: formatAmount(trend.maxDaily || 0, false),
            avgDaily: formatAmount(trend.avgDaily || 0, false)
          }
        })
      }
    } catch (error) {
      console.error('加载趋势数据失败:', error)
    }
  },

  // 加载构成数据
  async loadCompositionData(clubId: string, period: any) {
    try {
      const res = await api.callFunction('getEarningsComposition', {
        clubId,
        period
      }, false)
      
      if (res.success) {
        const composition = res.data.composition
        this.setData({
          compositionData: {
            gamePercentage: Math.round(composition.gamePercentage || 0),
            giftPercentage: Math.round(composition.giftPercentage || 0),
            otherPercentage: Math.round(composition.otherPercentage || 0)
          }
        })
      }
    } catch (error) {
      console.error('加载构成数据失败:', error)
    }
  },

  // 加载排名信息
  async loadRankingInfo(clubId: string, period: any) {
    try {
      const res = await api.callFunction('getRankingInfo', {
        clubId,
        period
      }, false)
      
      if (res.success) {
        const ranking = res.data.ranking
        this.setData({
          rankingInfo: {
            clubRank: ranking.clubRank || 0,
            totalMembers: ranking.totalMembers || 0,
            completedOrders: ranking.completedOrders || 0,
            satisfaction: Math.round(ranking.satisfaction || 0)
          }
        })
      }
    } catch (error) {
      console.error('加载排名信息失败:', error)
    }
  },

  // 加载收益记录
  async loadEarningsRecords(clubId: string, period: any) {
    try {
      const res = await api.callFunction('getEarningsRecords', {
        clubId,
        period,
        limit: 10
      }, false)
      
      if (res.success) {
        const records = (res.data.records || []).map((record: any) => ({
          ...record,
          typeText: this.getOrderTypeText(record.orderType),
          statusText: this.getOrderStatusText(record.status),
          date: getRelativeTime(record.createdAt),
          description: this.getRecordDescription(record),
          amountText: this.getAmountText(record),
          amountClass: this.getAmountClass(record)
        }))
        
        this.setData({ earningsRecords: records })
      }
    } catch (error) {
      console.error('加载收益记录失败:', error)
    }
  },

  // 查看全部记录
  viewAllRecords() {
    wx.switchTab({
      url: '/pages/companion/orders/orders'
    })
  },

  // 查看记录详情
  viewRecordDetail(e: any) {
    const id = e.currentTarget.dataset.id
    wx.navigateTo({
      url: `/pages/companion/order-detail/order-detail?id=${id}`
    })
  },

  // 获取订单类型文本
  getOrderTypeText(type: string) {
    const typeMap: { [key: string]: string } = {
      'gaming': '游戏单',
      'gift': '礼物单',
      'fine': '罚款',
      'group_fee': '团费',
      'deposit': '存单',
      'predeposit': '预存',
      'client_acquisition': '拉老板'
    }
    return typeMap[type] || '未知'
  },

  // 获取订单状态文本
  getOrderStatusText(status: string) {
    const statusMap: { [key: string]: string } = {
      'pending': '待审核',
      'approved': '已通过',
      'rejected': '已拒绝',
      'completed': '已完成'
    }
    return statusMap[status] || '未知'
  },

  // 获取记录描述
  getRecordDescription(record: any) {
    switch (record.orderType) {
      case 'gaming':
        return `${record.gameInfo?.gameName || ''} ${record.gameInfo?.duration || 0}分钟`
      case 'gift':
        return `${record.giftInfo?.giftName || ''} x${record.giftInfo?.quantity || 1}`
      case 'fine':
        return record.fineInfo?.reason || '罚款'
      case 'group_fee':
        return `${record.feeInfo?.period || ''} 团费`
      default:
        return '收益记录'
    }
  },

  // 获取金额文本
  getAmountText(record: any) {
    let amount = 0
    
    switch (record.orderType) {
      case 'gaming':
      case 'gift':
        amount = record.financial?.companionEarning || 0
        break
      case 'fine':
        amount = -(record.fineInfo?.amount || 0)
        break
      case 'group_fee':
        amount = -(record.feeInfo?.amount || 0)
        break
      default:
        amount = record.amount || 0
    }
    
    return formatAmount(Math.abs(amount))
  },

  // 获取金额样式类
  getAmountClass(record: any) {
    switch (record.orderType) {
      case 'gaming':
      case 'gift':
      case 'client_acquisition':
        return 'positive'
      case 'fine':
      case 'group_fee':
        return 'negative'
      default:
        return 'normal'
    }
  }
})
