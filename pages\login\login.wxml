<!--pages/login/login.wxml-->
<view class="container">
  <view class="login-container">
    <!-- Logo区域 -->
    <view class="logo-section">
      <image class="logo" src="/assets/images/logo.png" mode="aspectFit"></image>
      <text class="app-name">陪玩管理系统</text>
      <text class="app-desc">专业的陪玩服务管理平台</text>
    </view>

    <!-- 登录表单 -->
    <view class="login-form">
      <view class="welcome-text">
        <text class="welcome-title">欢迎使用</text>
        <text class="welcome-desc">请授权登录以继续使用</text>
      </view>

      <!-- 微信登录按钮 -->
      <button 
        class="login-btn" 
        open-type="getUserProfile"
        bindgetuserinfo="onGetUserProfile"
        wx:if="{{!userInfo}}"
      >
        <image class="wechat-icon" src="/assets/icons/wechat.png"></image>
        微信授权登录
      </button>

      <!-- 已获取用户信息，显示用户头像和昵称 -->
      <view class="user-info" wx:if="{{userInfo}}">
        <image class="avatar" src="{{userInfo.avatarUrl}}" mode="aspectFill"></image>
        <text class="nickname">{{userInfo.nickName}}</text>
      </view>

      <!-- 角色选择 -->
      <view class="role-selection" wx:if="{{userInfo && !isLogging}}">
        <text class="role-title">请选择您的身份</text>
        <view class="role-options">
          <view 
            class="role-option {{selectedRole === 'companion' ? 'selected' : ''}}"
            data-role="companion"
            bindtap="selectRole"
          >
            <image class="role-icon" src="/assets/icons/companion.png"></image>
            <text class="role-name">陪玩师</text>
            <text class="role-desc">提交报单，查看收益</text>
          </view>
          
          <view 
            class="role-option {{selectedRole === 'manager' ? 'selected' : ''}}"
            data-role="manager"
            bindtap="selectRole"
          >
            <image class="role-icon" src="/assets/icons/manager.png"></image>
            <text class="role-name">俱乐部管理员</text>
            <text class="role-desc">管理俱乐部，审核报单</text>
          </view>
        </view>

        <!-- 确认登录按钮 -->
        <button 
          class="confirm-btn {{selectedRole ? 'active' : ''}}" 
          bindtap="confirmLogin"
          disabled="{{!selectedRole || isLogging}}"
        >
          {{isLogging ? '登录中...' : '确认登录'}}
        </button>
      </view>

      <!-- 加载状态 -->
      <view class="loading-section" wx:if="{{isLogging}}">
        <view class="loading-spinner"></view>
        <text class="loading-text">正在登录...</text>
      </view>
    </view>

    <!-- 底部信息 -->
    <view class="footer">
      <text class="version">版本 1.0.0</text>
      <text class="copyright">© 2024 陪玩管理系统</text>
    </view>
  </view>
</view>
