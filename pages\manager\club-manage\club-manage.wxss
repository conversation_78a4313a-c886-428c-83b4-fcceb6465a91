/* pages/manager/club-manage/club-manage.wxss */

.container {
  background-color: #f8f8f8;
  min-height: 100vh;
  padding-bottom: 200rpx;
}

/* 俱乐部信息 */
.club-info-section {
  padding: 20rpx 30rpx;
}

.club-card {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.club-header {
  display: flex;
  align-items: flex-start;
  margin-bottom: 20rpx;
  position: relative;
}

.club-logo {
  width: 100rpx;
  height: 100rpx;
  border-radius: 16rpx;
  margin-right: 20rpx;
  background-color: #f0f0f0;
}

.club-details {
  flex: 1;
}

.club-name {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 8rpx;
  display: block;
}

.club-status {
  display: inline-block;
  padding: 6rpx 12rpx;
  border-radius: 12rpx;
  font-size: 22rpx;
  color: #fff;
  margin-bottom: 8rpx;
}

.status-active {
  background-color: #1AAD19;
}

.status-suspended {
  background-color: #FF9500;
}

.status-closed {
  background-color: #FF6B6B;
}

.club-date {
  font-size: 24rpx;
  color: #999;
  display: block;
}

.edit-btn {
  position: absolute;
  top: 0;
  right: 0;
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.edit-icon {
  width: 32rpx;
  height: 32rpx;
}

.club-description {
  padding-top: 20rpx;
  border-top: 1rpx solid #f0f0f0;
}

.description-text {
  font-size: 26rpx;
  color: #666;
  line-height: 1.6;
}

/* 统计区域 */
.stats-section {
  padding: 0 30rpx 20rpx;
}

.stats-grid {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 30rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.stats-card {
  text-align: center;
}

.stats-number {
  font-size: 48rpx;
  font-weight: 700;
  color: #333;
  margin-bottom: 8rpx;
  display: block;
}

.stats-number.amount-positive {
  color: #1AAD19;
}

.stats-label {
  font-size: 24rpx;
  color: #666;
}

/* 设置区域 */
.settings-section,
.working-hours-section,
.invite-section,
.danger-section {
  padding: 0 30rpx 20rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 20rpx;
}

.settings-card,
.working-hours-card,
.invite-card,
.danger-card {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.setting-item,
.time-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.setting-item:last-child,
.time-item:last-child {
  border-bottom: none;
}

.setting-label,
.time-label {
  font-size: 28rpx;
  color: #333;
}

.setting-value {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.value-text {
  font-size: 28rpx;
  color: #666;
}

.arrow-icon {
  width: 24rpx;
  height: 24rpx;
}

.time-picker {
  display: flex;
  align-items: center;
  gap: 16rpx;
  font-size: 28rpx;
  color: #333;
}

/* 邀请码 */
.invite-code-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
}

.invite-label {
  font-size: 28rpx;
  color: #333;
}

.invite-code {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.code-text {
  font-size: 32rpx;
  font-weight: 600;
  color: #1AAD19;
  font-family: 'Courier New', monospace;
}

.copy-btn {
  background-color: #1AAD19;
  color: #fff;
  border: none;
  border-radius: 8rpx;
  padding: 12rpx 20rpx;
  font-size: 24rpx;
}

.refresh-btn {
  width: 100%;
  background-color: #f0f0f0;
  color: #333;
  border: none;
  border-radius: 12rpx;
  padding: 20rpx;
  font-size: 28rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12rpx;
}

.refresh-icon {
  width: 32rpx;
  height: 32rpx;
}

/* 危险操作 */
.danger-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.danger-item:last-child {
  border-bottom: none;
}

.danger-info {
  flex: 1;
}

.danger-title {
  font-size: 28rpx;
  color: #FF6B6B;
  font-weight: 500;
  margin-bottom: 8rpx;
  display: block;
}

.danger-desc {
  font-size: 24rpx;
  color: #999;
}

/* 保存按钮 */
.save-section {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #fff;
  padding: 20rpx 30rpx;
  border-top: 1rpx solid #f0f0f0;
}

.save-btn {
  width: 100%;
  height: 88rpx;
  background-color: #1AAD19;
  color: #fff;
  border: none;
  border-radius: 12rpx;
  font-size: 32rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 编辑弹窗 */
.edit-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  background-color: #fff;
  border-radius: 16rpx;
  width: 600rpx;
  max-height: 80vh;
  overflow: hidden;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.modal-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.close-icon {
  width: 40rpx;
  height: 40rpx;
}

.modal-body {
  padding: 30rpx;
}

.form-group {
  margin-bottom: 30rpx;
}

.form-label {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 12rpx;
  display: block;
}

.form-input {
  width: 100%;
  padding: 20rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 8rpx;
  font-size: 28rpx;
  background-color: #fff;
}

.form-input:focus {
  border-color: #1AAD19;
}

.form-textarea {
  width: 100%;
  padding: 20rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 8rpx;
  font-size: 28rpx;
  background-color: #fff;
  min-height: 120rpx;
}

.form-tip {
  font-size: 24rpx;
  color: #999;
  margin-top: 12rpx;
}

.modal-footer {
  display: flex;
  padding: 30rpx;
  gap: 20rpx;
  border-top: 1rpx solid #f0f0f0;
}

.modal-btn {
  flex: 1;
  height: 80rpx;
  border-radius: 12rpx;
  font-size: 28rpx;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-btn.cancel {
  background-color: #f0f0f0;
  color: #333;
}

.modal-btn.confirm {
  background-color: #1AAD19;
  color: #fff;
}
