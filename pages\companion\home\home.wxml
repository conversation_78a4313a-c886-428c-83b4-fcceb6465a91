<!--pages/companion/home/<USER>
<view class="container">
  <!-- 头部区域 -->
  <view class="header">
    <view class="header-content">
      <view class="user-info">
        <image class="avatar" src="{{userInfo.avatarUrl}}" mode="aspectFill"></image>
        <view class="user-details">
          <text class="nickname">{{userInfo.nickName}}</text>
          <text class="role">陪玩师</text>
        </view>
      </view>
      
      <view class="header-actions">
        <view class="club-switch" bindtap="showClubSwitch">
          <text class="club-name">{{currentClub.name || '选择俱乐部'}}</text>
          <image class="switch-icon" src="/assets/icons/switch.png"></image>
        </view>
        
        <view class="notification" bindtap="showNotifications">
          <image class="notification-icon" src="/assets/icons/notification.png"></image>
          <view class="notification-badge" wx:if="{{unreadCount > 0}}">{{unreadCount}}</view>
        </view>
      </view>
    </view>
  </view>

  <!-- 今日数据概览 -->
  <view class="stats-section">
    <view class="section-title">
      <text class="title">今日数据</text>
      <text class="date">{{todayDate}}</text>
    </view>
    
    <view class="stats-grid">
      <view class="stats-card">
        <text class="stats-number">{{todayStats.gameOrders}}</text>
        <text class="stats-label">游戏单</text>
      </view>
      
      <view class="stats-card">
        <text class="stats-number">{{todayStats.giftOrders}}</text>
        <text class="stats-label">礼物单</text>
      </view>
      
      <view class="stats-card">
        <text class="stats-number amount-positive">¥{{todayStats.totalEarnings}}</text>
        <text class="stats-label">总收益</text>
      </view>
      
      <view class="stats-card">
        <text class="stats-number amount-normal">¥{{todayStats.pendingAmount}}</text>
        <text class="stats-label">待结算</text>
      </view>
    </view>
  </view>

  <!-- 最新公告 -->
  <view class="announcement-section" wx:if="{{announcements.length > 0}}">
    <view class="section-title">
      <text class="title">📢 最新公告</text>
      <text class="more" bindtap="viewAllAnnouncements">查看全部</text>
    </view>
    
    <view class="announcement-list">
      <view 
        class="announcement-item" 
        wx:for="{{announcements}}" 
        wx:key="id"
        bindtap="viewAnnouncement"
        data-id="{{item.id}}"
      >
        <view class="announcement-content">
          <text class="announcement-title">{{item.title}}</text>
          <text class="announcement-time">{{item.publishedAt}}</text>
        </view>
        <view class="announcement-badge" wx:if="{{!item.isRead}}"></view>
      </view>
    </view>
  </view>

  <!-- 快速报单 -->
  <view class="quick-actions-section">
    <view class="section-title">
      <text class="title">🎮 快速报单</text>
    </view>
    
    <view class="quick-actions">
      <view class="action-item" bindtap="createGameOrder">
        <image class="action-icon" src="/assets/icons/game.png"></image>
        <text class="action-text">游戏单</text>
      </view>
      
      <view class="action-item" bindtap="createGiftOrder">
        <image class="action-icon" src="/assets/icons/gift.png"></image>
        <text class="action-text">礼物单</text>
      </view>
      
      <view class="action-item" bindtap="createDepositRecord">
        <image class="action-icon" src="/assets/icons/deposit.png"></image>
        <text class="action-text">存单</text>
      </view>
      
      <view class="action-item" bindtap="createClientRecord">
        <image class="action-icon" src="/assets/icons/client.png"></image>
        <text class="action-text">拉老板</text>
      </view>
    </view>
  </view>

  <!-- 本周收益趋势 -->
  <view class="earnings-trend-section">
    <view class="section-title">
      <text class="title">📈 本周收益趋势</text>
      <text class="more" bindtap="viewEarningsDetail">详情</text>
    </view>
    
    <view class="trend-chart">
      <!-- 这里可以集成图表组件 -->
      <view class="chart-placeholder">
        <text class="chart-text">收益趋势图</text>
        <text class="chart-desc">本周总收益: ¥{{weeklyEarnings}}</text>
      </view>
    </view>
  </view>

  <!-- 最近订单 -->
  <view class="recent-orders-section">
    <view class="section-title">
      <text class="title">📋 最近订单</text>
      <text class="more" bindtap="viewAllOrders">查看全部</text>
    </view>
    
    <view class="order-list" wx:if="{{recentOrders.length > 0}}">
      <view 
        class="order-item" 
        wx:for="{{recentOrders}}" 
        wx:key="id"
        bindtap="viewOrderDetail"
        data-id="{{item.id}}"
      >
        <view class="order-info">
          <view class="order-header">
            <text class="order-type">{{item.typeText}}</text>
            <text class="order-status status-{{item.status}}">{{item.statusText}}</text>
          </view>
          
          <text class="order-desc">{{item.description}}</text>
          
          <view class="order-footer">
            <text class="order-time">{{item.createdAt}}</text>
            <text class="order-amount amount-positive">¥{{item.amount}}</text>
          </view>
        </view>
      </view>
    </view>
    
    <view class="empty-state" wx:else>
      <text class="empty-text">暂无订单记录</text>
    </view>
  </view>
</view>

<!-- 俱乐部切换弹窗 -->
<view class="club-switch-modal" wx:if="{{showClubSwitchModal}}" bindtap="hideClubSwitch">
  <view class="modal-content" catchtap="stopPropagation">
    <view class="modal-header">
      <text class="modal-title">选择俱乐部</text>
      <image class="close-icon" src="/assets/icons/close.png" bindtap="hideClubSwitch"></image>
    </view>
    
    <view class="club-list">
      <view 
        class="club-item {{item.id === currentClub.id ? 'selected' : ''}}"
        wx:for="{{clubs}}"
        wx:key="id"
        bindtap="switchClub"
        data-club="{{item}}"
      >
        <view class="club-info">
          <text class="club-name">{{item.name}}</text>
          <text class="club-desc">成员: {{item.memberCount}}人 | 本月收益: ¥{{item.monthlyEarnings}}</text>
        </view>
        <image class="check-icon" src="/assets/icons/check.png" wx:if="{{item.id === currentClub.id}}"></image>
      </view>
    </view>
  </view>
</view>
