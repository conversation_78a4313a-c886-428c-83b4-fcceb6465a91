{"rules": {"users": {"read": "auth != null && (resource.data.openid == auth.openid || get('database').collection('club_members').where({userId: resource.data._id, status: 'active'}).get().data.length > 0)", "write": "auth != null && resource.data.openid == auth.openid"}, "clubs": {"read": "auth != null && (resource.data.ownerId == get('database').collection('users').where({openid: auth.openid}).get().data[0]._id || get('database').collection('club_members').where({clubId: resource.data._id, userId: get('database').collection('users').where({openid: auth.openid}).get().data[0]._id, status: 'active'}).get().data.length > 0)", "write": "auth != null && resource.data.ownerId == get('database').collection('users').where({openid: auth.openid}).get().data[0]._id"}, "club_members": {"read": "auth != null && (resource.data.userId == get('database').collection('users').where({openid: auth.openid}).get().data[0]._id || get('database').collection('club_members').where({clubId: resource.data.clubId, userId: get('database').collection('users').where({openid: auth.openid}).get().data[0]._id, role: 'admin'}).get().data.length > 0)", "write": "auth != null && get('database').collection('clubs').doc(resource.data.clubId).get().data.ownerId == get('database').collection('users').where({openid: auth.openid}).get().data[0]._id"}, "game_orders": {"read": "auth != null && (resource.data.companionId == get('database').collection('users').where({openid: auth.openid}).get().data[0]._id || get('database').collection('club_members').where({clubId: resource.data.clubId, userId: get('database').collection('users').where({openid: auth.openid}).get().data[0]._id, role: db.command.in(['admin', 'owner'])}).get().data.length > 0)", "write": "auth != null && resource.data.companionId == get('database').collection('users').where({openid: auth.openid}).get().data[0]._id"}, "gift_orders": {"read": "auth != null && (resource.data.companionId == get('database').collection('users').where({openid: auth.openid}).get().data[0]._id || get('database').collection('club_members').where({clubId: resource.data.clubId, userId: get('database').collection('users').where({openid: auth.openid}).get().data[0]._id, role: db.command.in(['admin', 'owner'])}).get().data.length > 0)", "write": "auth != null && resource.data.companionId == get('database').collection('users').where({openid: auth.openid}).get().data[0]._id"}, "fine_records": {"read": "auth != null && (resource.data.companionId == get('database').collection('users').where({openid: auth.openid}).get().data[0]._id || get('database').collection('club_members').where({clubId: resource.data.clubId, userId: get('database').collection('users').where({openid: auth.openid}).get().data[0]._id, role: db.command.in(['admin', 'owner'])}).get().data.length > 0)", "write": "auth != null && get('database').collection('club_members').where({clubId: resource.data.clubId, userId: get('database').collection('users').where({openid: auth.openid}).get().data[0]._id, role: db.command.in(['admin', 'owner'])}).get().data.length > 0"}, "group_fee_records": {"read": "auth != null && (resource.data.companionId == get('database').collection('users').where({openid: auth.openid}).get().data[0]._id || get('database').collection('club_members').where({clubId: resource.data.clubId, userId: get('database').collection('users').where({openid: auth.openid}).get().data[0]._id, role: db.command.in(['admin', 'owner'])}).get().data.length > 0)", "write": "auth != null && get('database').collection('club_members').where({clubId: resource.data.clubId, userId: get('database').collection('users').where({openid: auth.openid}).get().data[0]._id, role: db.command.in(['admin', 'owner'])}).get().data.length > 0"}, "deposit_records": {"read": "auth != null && (resource.data.companionId == get('database').collection('users').where({openid: auth.openid}).get().data[0]._id || get('database').collection('club_members').where({clubId: resource.data.clubId, userId: get('database').collection('users').where({openid: auth.openid}).get().data[0]._id, role: db.command.in(['admin', 'owner'])}).get().data.length > 0)", "write": "auth != null && resource.data.companionId == get('database').collection('users').where({openid: auth.openid}).get().data[0]._id"}, "predeposit_records": {"read": "auth != null && (resource.data.companionId == get('database').collection('users').where({openid: auth.openid}).get().data[0]._id || get('database').collection('club_members').where({clubId: resource.data.clubId, userId: get('database').collection('users').where({openid: auth.openid}).get().data[0]._id, role: db.command.in(['admin', 'owner'])}).get().data.length > 0)", "write": "auth != null && resource.data.companionId == get('database').collection('users').where({openid: auth.openid}).get().data[0]._id"}, "client_acquisition_records": {"read": "auth != null && (resource.data.companionId == get('database').collection('users').where({openid: auth.openid}).get().data[0]._id || get('database').collection('club_members').where({clubId: resource.data.clubId, userId: get('database').collection('users').where({openid: auth.openid}).get().data[0]._id, role: db.command.in(['admin', 'owner'])}).get().data.length > 0)", "write": "auth != null && resource.data.companionId == get('database').collection('users').where({openid: auth.openid}).get().data[0]._id"}, "announcements": {"read": "auth != null && (resource.data.clubId == null || get('database').collection('club_members').where({clubId: resource.data.clubId, userId: get('database').collection('users').where({openid: auth.openid}).get().data[0]._id, status: 'active'}).get().data.length > 0)", "write": "auth != null && get('database').collection('club_members').where({clubId: resource.data.clubId, userId: get('database').collection('users').where({openid: auth.openid}).get().data[0]._id, role: db.command.in(['admin', 'owner'])}).get().data.length > 0"}, "financial_settlements": {"read": "auth != null && (resource.data.companionId == get('database').collection('users').where({openid: auth.openid}).get().data[0]._id || get('database').collection('club_members').where({clubId: resource.data.clubId, userId: get('database').collection('users').where({openid: auth.openid}).get().data[0]._id, role: db.command.in(['admin', 'owner'])}).get().data.length > 0)", "write": "auth != null && get('database').collection('club_members').where({clubId: resource.data.clubId, userId: get('database').collection('users').where({openid: auth.openid}).get().data[0]._id, role: db.command.in(['admin', 'owner'])}).get().data.length > 0"}}}