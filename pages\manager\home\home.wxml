<!--pages/manager/home/<USER>
<view class="container">
  <!-- 头部区域 -->
  <view class="header">
    <view class="header-content">
      <view class="user-info">
        <image class="avatar" src="{{userInfo.avatarUrl}}" mode="aspectFill"></image>
        <view class="user-details">
          <text class="nickname">{{userInfo.nickName}}</text>
          <text class="role">俱乐部管理员</text>
        </view>
      </view>
      
      <view class="header-actions">
        <view class="club-info" bindtap="manageClub">
          <text class="club-name">{{currentClub.name || '未选择俱乐部'}}</text>
          <image class="manage-icon" src="/assets/icons/settings.png"></image>
        </view>
        
        <view class="notification" bindtap="showNotifications">
          <image class="notification-icon" src="/assets/icons/notification.png"></image>
          <view class="notification-badge" wx:if="{{unreadCount > 0}}">{{unreadCount}}</view>
        </view>
      </view>
    </view>
  </view>

  <!-- 今日数据总览 -->
  <view class="stats-section">
    <view class="section-title">
      <text class="title">今日数据总览</text>
      <text class="date">{{todayDate}}</text>
    </view>
    
    <view class="stats-grid">
      <view class="stats-card">
        <text class="stats-number">{{todayStats.totalOrders}}</text>
        <text class="stats-label">总订单</text>
      </view>
      
      <view class="stats-card">
        <text class="stats-number amount-positive">¥{{todayStats.totalRevenue}}</text>
        <text class="stats-label">总收益</text>
      </view>
      
      <view class="stats-card">
        <text class="stats-number">{{todayStats.activeMembers}}</text>
        <text class="stats-label">活跃人数</text>
      </view>
      
      <view class="stats-card">
        <text class="stats-number pending">{{todayStats.pendingOrders}}</text>
        <text class="stats-label">待审核</text>
      </view>
    </view>
  </view>

  <!-- 待处理事项 -->
  <view class="pending-section" wx:if="{{pendingItems.length > 0}}">
    <view class="section-title">
      <text class="title">🚨 待处理事项</text>
      <text class="more" bindtap="viewAllPending">查看全部</text>
    </view>
    
    <view class="pending-list">
      <view 
        class="pending-item" 
        wx:for="{{pendingItems}}" 
        wx:key="id"
        bindtap="handlePendingItem"
        data-item="{{item}}"
      >
        <view class="pending-icon">
          <image class="icon" src="{{item.icon}}"></image>
        </view>
        <view class="pending-content">
          <text class="pending-title">{{item.title}}</text>
          <text class="pending-desc">{{item.description}}</text>
        </view>
        <view class="pending-count">{{item.count}}</view>
      </view>
    </view>
  </view>

  <!-- 快速操作 -->
  <view class="quick-actions-section">
    <view class="section-title">
      <text class="title">🎯 快速操作</text>
    </view>
    
    <view class="quick-actions">
      <view class="action-row">
        <view class="action-item" bindtap="reviewOrders">
          <image class="action-icon" src="/assets/icons/review.png"></image>
          <text class="action-text">审核报单</text>
        </view>
        
        <view class="action-item" bindtap="publishAnnouncement">
          <image class="action-icon" src="/assets/icons/announcement.png"></image>
          <text class="action-text">发布公告</text>
        </view>
      </view>
      
      <view class="action-row">
        <view class="action-item" bindtap="manageMembers">
          <image class="action-icon" src="/assets/icons/members.png"></image>
          <text class="action-text">成员管理</text>
        </view>
        
        <view class="action-item" bindtap="financialManagement">
          <image class="action-icon" src="/assets/icons/financial.png"></image>
          <text class="action-text">财务结算</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 本周业绩趋势 -->
  <view class="performance-section">
    <view class="section-title">
      <text class="title">📈 本周业绩趋势</text>
      <text class="more" bindtap="viewStatistics">详细统计</text>
    </view>
    
    <view class="performance-chart">
      <!-- 这里可以集成图表组件 -->
      <view class="chart-placeholder">
        <text class="chart-text">业绩趋势图</text>
        <view class="chart-summary">
          <text class="summary-item">本周总收益: ¥{{weeklyStats.totalRevenue}}</text>
          <text class="summary-item">环比上周: {{weeklyStats.growthRate}}%</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 成员表现排行 -->
  <view class="ranking-section">
    <view class="section-title">
      <text class="title">🏆 成员表现排行</text>
      <text class="more" bindtap="viewFullRanking">完整排行</text>
    </view>
    
    <view class="ranking-list" wx:if="{{memberRanking.length > 0}}">
      <view 
        class="ranking-item" 
        wx:for="{{memberRanking}}" 
        wx:key="id"
        bindtap="viewMemberDetail"
        data-id="{{item.id}}"
      >
        <view class="ranking-position">
          <text class="position-number">{{index + 1}}</text>
        </view>
        
        <view class="member-info">
          <image class="member-avatar" src="{{item.avatar}}" mode="aspectFill"></image>
          <view class="member-details">
            <text class="member-name">{{item.nickname}}</text>
            <text class="member-stats">本周{{item.weeklyOrders}}单 | ¥{{item.weeklyEarnings}}</text>
          </view>
        </view>
        
        <view class="ranking-badge" wx:if="{{index < 3}}">
          <image class="badge-icon" src="/assets/icons/medal-{{index + 1}}.png"></image>
        </view>
      </view>
    </view>
    
    <view class="empty-state" wx:else>
      <text class="empty-text">暂无成员数据</text>
    </view>
  </view>

  <!-- 最新动态 -->
  <view class="activity-section">
    <view class="section-title">
      <text class="title">📋 最新动态</text>
      <text class="more" bindtap="viewAllActivities">查看全部</text>
    </view>
    
    <view class="activity-list" wx:if="{{recentActivities.length > 0}}">
      <view 
        class="activity-item" 
        wx:for="{{recentActivities}}" 
        wx:key="id"
      >
        <view class="activity-time">{{item.time}}</view>
        <view class="activity-content">
          <text class="activity-text">{{item.description}}</text>
          <text class="activity-type">{{item.type}}</text>
        </view>
      </view>
    </view>
    
    <view class="empty-state" wx:else>
      <text class="empty-text">暂无最新动态</text>
    </view>
  </view>
</view>
