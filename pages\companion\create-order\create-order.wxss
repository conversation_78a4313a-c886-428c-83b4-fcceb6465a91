/* pages/companion/create-order/create-order.wxss */

.container {
  background-color: #f8f8f8;
  min-height: 100vh;
  padding-bottom: 200rpx;
}

/* 头部 */
.header {
  background-color: #fff;
  padding: 30rpx;
  text-align: center;
  border-bottom: 1rpx solid #f0f0f0;
}

.page-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
}

/* 表单区域 */
.form-section {
  background-color: #fff;
  margin: 20rpx 30rpx;
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 30rpx;
  display: flex;
  align-items: center;
}

/* 表单组件 */
.form-group {
  margin-bottom: 30rpx;
}

.form-label {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 12rpx;
  display: block;
}

.form-input {
  width: 100%;
  padding: 20rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 8rpx;
  font-size: 28rpx;
  background-color: #fff;
}

.form-input:focus {
  border-color: #1AAD19;
}

.form-textarea {
  width: 100%;
  padding: 20rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 8rpx;
  font-size: 28rpx;
  background-color: #fff;
  min-height: 120rpx;
}

.picker-input {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 8rpx;
  font-size: 28rpx;
  background-color: #fff;
}

.picker-input.placeholder {
  color: #999;
}

.picker-arrow {
  width: 24rpx;
  height: 24rpx;
}

/* 时长输入 */
.duration-input {
  display: flex;
  align-items: center;
}

.duration-unit {
  margin-left: 16rpx;
  font-size: 28rpx;
  color: #666;
}

/* 金额输入 */
.amount-input {
  display: flex;
  align-items: center;
}

.amount-symbol {
  margin-right: 8rpx;
  font-size: 28rpx;
  color: #333;
  font-weight: 600;
}

/* 金额分解 */
.amount-breakdown {
  background-color: #f8f9fa;
  border-radius: 12rpx;
  padding: 20rpx;
  margin-top: 20rpx;
}

.breakdown-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12rpx;
}

.breakdown-item:last-child {
  margin-bottom: 0;
}

.breakdown-label {
  font-size: 26rpx;
  color: #666;
}

.breakdown-value {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
}

.breakdown-value.positive {
  color: #1AAD19;
}

/* 图片上传 */
.image-upload {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
  margin-top: 20rpx;
}

.upload-item {
  position: relative;
  width: 200rpx;
  height: 200rpx;
}

.upload-image {
  width: 100%;
  height: 100%;
  border-radius: 8rpx;
  background-color: #f0f0f0;
}

.delete-btn {
  position: absolute;
  top: -8rpx;
  right: -8rpx;
  width: 40rpx;
  height: 40rpx;
  background-color: #FF6B6B;
  color: #fff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  font-weight: 600;
}

.upload-btn {
  width: 200rpx;
  height: 200rpx;
  border: 2rpx dashed #e0e0e0;
  border-radius: 8rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: #f8f9fa;
}

.upload-icon {
  width: 60rpx;
  height: 60rpx;
  margin-bottom: 12rpx;
}

.upload-text {
  font-size: 24rpx;
  color: #999;
}

.upload-tip {
  font-size: 22rpx;
  color: #999;
  margin-top: 12rpx;
}

/* 提交区域 */
.submit-section {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #fff;
  padding: 20rpx 30rpx;
  border-top: 1rpx solid #f0f0f0;
  display: flex;
  gap: 20rpx;
}

.submit-btn {
  flex: 2;
  height: 88rpx;
  background-color: #1AAD19;
  color: #fff;
  border: none;
  border-radius: 12rpx;
  font-size: 32rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.cancel-btn {
  flex: 1;
  height: 88rpx;
  background-color: #f0f0f0;
  color: #333;
  border: none;
  border-radius: 12rpx;
  font-size: 32rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
