// 云函数入口文件
const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()

// 云函数入口函数
exports.main = async (event, context) => {
  const wxContext = cloud.getWXContext()
  
  try {
    const { code } = event
    
    // 获取用户openid和session_key
    const { openid, sessionKey } = wxContext
    
    // 检查用户是否已存在
    const userQuery = await db.collection('users').where({
      openid: openid
    }).get()
    
    let user = null
    
    if (userQuery.data.length > 0) {
      // 用户已存在，更新最后登录时间
      user = userQuery.data[0]
      await db.collection('users').doc(user._id).update({
        data: {
          lastLoginAt: new Date(),
          updatedAt: new Date()
        }
      })
    }
    
    return {
      success: true,
      data: {
        openid: openid,
        sessionKey: sessionKey,
        user: user
      },
      message: '登录成功'
    }
    
  } catch (error) {
    console.error('登录失败:', error)
    return {
      success: false,
      message: '登录失败: ' + error.message
    }
  }
}
