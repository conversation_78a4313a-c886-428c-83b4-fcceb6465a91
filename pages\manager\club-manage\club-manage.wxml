<!--pages/manager/club-manage/club-manage.wxml-->
<view class="container">
  <!-- 俱乐部基本信息 -->
  <view class="club-info-section">
    <view class="club-card">
      <view class="club-header">
        <image class="club-logo" src="{{clubInfo.logo || '/assets/images/default-club.png'}}" mode="aspectFill"></image>
        <view class="club-details">
          <text class="club-name">{{clubInfo.name}}</text>
          <text class="club-status status-{{clubInfo.status}}">{{clubInfo.statusText}}</text>
          <text class="club-date">创建于 {{clubInfo.createdAt}}</text>
        </view>
        <view class="edit-btn" bindtap="editClubInfo">
          <image class="edit-icon" src="/assets/icons/edit.png"></image>
        </view>
      </view>
      
      <view class="club-description" wx:if="{{clubInfo.description}}">
        <text class="description-text">{{clubInfo.description}}</text>
      </view>
    </view>
  </view>

  <!-- 俱乐部统计 -->
  <view class="stats-section">
    <view class="stats-grid">
      <view class="stats-card">
        <text class="stats-number">{{clubStats.memberCount}}</text>
        <text class="stats-label">成员数量</text>
      </view>
      
      <view class="stats-card">
        <text class="stats-number">{{clubStats.totalOrders}}</text>
        <text class="stats-label">总订单</text>
      </view>
      
      <view class="stats-card">
        <text class="stats-number amount-positive">¥{{clubStats.totalRevenue}}</text>
        <text class="stats-label">总收益</text>
      </view>
      
      <view class="stats-card">
        <text class="stats-number amount-positive">¥{{clubStats.totalCommission}}</text>
        <text class="stats-label">总佣金</text>
      </view>
    </view>
  </view>

  <!-- 俱乐部设置 -->
  <view class="settings-section">
    <view class="section-title">俱乐部设置</view>
    
    <view class="settings-card">
      <view class="setting-item">
        <text class="setting-label">最大成员数</text>
        <view class="setting-value">
          <text class="value-text">{{clubSettings.maxMembers}}人</text>
          <image class="arrow-icon" src="/assets/icons/arrow-right.png" bindtap="editMaxMembers"></image>
        </view>
      </view>
      
      <view class="setting-item">
        <text class="setting-label">佣金比例</text>
        <view class="setting-value">
          <text class="value-text">{{clubSettings.commissionRate}}%</text>
          <image class="arrow-icon" src="/assets/icons/arrow-right.png" bindtap="editCommissionRate"></image>
        </view>
      </view>
      
      <view class="setting-item">
        <text class="setting-label">自动审批</text>
        <switch 
          checked="{{clubSettings.autoApprove}}" 
          bindchange="onAutoApproveChange"
          color="#1AAD19"
        ></switch>
      </view>
      
      <view class="setting-item">
        <text class="setting-label">允许多俱乐部</text>
        <switch 
          checked="{{clubSettings.allowMultipleClubs}}" 
          bindchange="onAllowMultipleChange"
          color="#1AAD19"
        ></switch>
      </view>
    </view>
  </view>

  <!-- 工作时间设置 -->
  <view class="working-hours-section">
    <view class="section-title">工作时间</view>
    
    <view class="working-hours-card">
      <view class="time-item">
        <text class="time-label">开始时间</text>
        <picker 
          mode="time" 
          value="{{clubSettings.workingHours.start}}"
          bindchange="onStartTimeChange"
        >
          <view class="time-picker">
            {{clubSettings.workingHours.start}}
            <image class="arrow-icon" src="/assets/icons/arrow-right.png"></image>
          </view>
        </picker>
      </view>
      
      <view class="time-item">
        <text class="time-label">结束时间</text>
        <picker 
          mode="time" 
          value="{{clubSettings.workingHours.end}}"
          bindchange="onEndTimeChange"
        >
          <view class="time-picker">
            {{clubSettings.workingHours.end}}
            <image class="arrow-icon" src="/assets/icons/arrow-right.png"></image>
          </view>
        </picker>
      </view>
    </view>
  </view>

  <!-- 邀请码管理 -->
  <view class="invite-section">
    <view class="section-title">邀请码管理</view>
    
    <view class="invite-card">
      <view class="invite-code-item">
        <text class="invite-label">当前邀请码</text>
        <view class="invite-code">
          <text class="code-text">{{inviteCode}}</text>
          <button class="copy-btn" size="mini" bindtap="copyInviteCode">复制</button>
        </view>
      </view>
      
      <button class="refresh-btn" bindtap="refreshInviteCode">
        <image class="refresh-icon" src="/assets/icons/refresh.png"></image>
        重新生成
      </button>
    </view>
  </view>

  <!-- 危险操作 -->
  <view class="danger-section">
    <view class="section-title">危险操作</view>
    
    <view class="danger-card">
      <view class="danger-item" bindtap="transferOwnership">
        <view class="danger-info">
          <text class="danger-title">转让俱乐部</text>
          <text class="danger-desc">将俱乐部所有权转让给其他成员</text>
        </view>
        <image class="arrow-icon" src="/assets/icons/arrow-right.png"></image>
      </view>
      
      <view class="danger-item" bindtap="dissolveClub">
        <view class="danger-info">
          <text class="danger-title">解散俱乐部</text>
          <text class="danger-desc">永久删除俱乐部，此操作不可恢复</text>
        </view>
        <image class="arrow-icon" src="/assets/icons/arrow-right.png"></image>
      </view>
    </view>
  </view>

  <!-- 保存按钮 -->
  <view class="save-section">
    <button class="save-btn" bindtap="saveSettings" loading="{{saving}}">
      {{saving ? '保存中...' : '保存设置'}}
    </button>
  </view>
</view>

<!-- 编辑弹窗 -->
<view class="edit-modal" wx:if="{{showEditModal}}" bindtap="hideEditModal">
  <view class="modal-content" catchtap="stopPropagation">
    <view class="modal-header">
      <text class="modal-title">{{editModalTitle}}</text>
      <image class="close-icon" src="/assets/icons/close.png" bindtap="hideEditModal"></image>
    </view>
    
    <view class="modal-body">
      <!-- 编辑俱乐部信息 -->
      <view wx:if="{{editType === 'clubInfo'}}">
        <view class="form-group">
          <text class="form-label">俱乐部名称</text>
          <input 
            class="form-input" 
            placeholder="请输入俱乐部名称"
            value="{{editForm.name}}"
            bindinput="onNameInput"
          />
        </view>
        
        <view class="form-group">
          <text class="form-label">俱乐部描述</text>
          <textarea 
            class="form-textarea" 
            placeholder="请输入俱乐部描述"
            value="{{editForm.description}}"
            bindinput="onDescriptionInput"
            maxlength="200"
          ></textarea>
        </view>
      </view>
      
      <!-- 编辑最大成员数 -->
      <view wx:elif="{{editType === 'maxMembers'}}">
        <view class="form-group">
          <text class="form-label">最大成员数</text>
          <input 
            class="form-input" 
            type="number"
            placeholder="请输入最大成员数"
            value="{{editForm.maxMembers}}"
            bindinput="onMaxMembersInput"
          />
        </view>
      </view>
      
      <!-- 编辑佣金比例 -->
      <view wx:elif="{{editType === 'commissionRate'}}">
        <view class="form-group">
          <text class="form-label">佣金比例 (%)</text>
          <input 
            class="form-input" 
            type="digit"
            placeholder="请输入佣金比例"
            value="{{editForm.commissionRate}}"
            bindinput="onCommissionRateInput"
          />
        </view>
        <text class="form-tip">建议设置在10%-20%之间</text>
      </view>
    </view>
    
    <view class="modal-footer">
      <button class="modal-btn cancel" bindtap="hideEditModal">取消</button>
      <button class="modal-btn confirm" bindtap="confirmEdit" loading="{{editing}}">
        {{editing ? '保存中...' : '确认'}}
      </button>
    </view>
  </view>
</view>
