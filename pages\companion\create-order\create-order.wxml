<!--pages/companion/create-order/create-order.wxml-->
<view class="container">
  <!-- 头部 -->
  <view class="header">
    <text class="page-title">{{pageTitle}}</text>
  </view>

  <form bindsubmit="onSubmit">
    <!-- 游戏单表单 -->
    <view class="form-section" wx:if="{{orderType === 'gaming'}}">
      <view class="section-title">🎮 游戏信息</view>
      
      <view class="form-group">
        <text class="form-label">游戏名称 *</text>
        <picker 
          mode="selector" 
          range="{{gameOptions}}" 
          range-key="name"
          value="{{formData.gameIndex}}"
          bindchange="onGameChange"
        >
          <view class="picker-input {{!formData.gameName ? 'placeholder' : ''}}">
            {{formData.gameName || '请选择游戏'}}
            <image class="picker-arrow" src="/assets/icons/arrow-down.png"></image>
          </view>
        </picker>
      </view>

      <view class="form-group">
        <text class="form-label">游戏模式 *</text>
        <picker 
          mode="selector" 
          range="{{gameModeOptions}}" 
          value="{{formData.gameModeIndex}}"
          bindchange="onGameModeChange"
        >
          <view class="picker-input {{!formData.gameMode ? 'placeholder' : ''}}">
            {{formData.gameMode || '请选择游戏模式'}}
            <image class="picker-arrow" src="/assets/icons/arrow-down.png"></image>
          </view>
        </picker>
      </view>

      <view class="form-group">
        <text class="form-label">游戏时长 *</text>
        <view class="duration-input">
          <input 
            class="form-input" 
            type="number" 
            placeholder="请输入时长"
            value="{{formData.duration}}"
            bindinput="onDurationInput"
          />
          <text class="duration-unit">分钟</text>
        </view>
      </view>

      <view class="form-group">
        <text class="form-label">段位要求</text>
        <input 
          class="form-input" 
          placeholder="如：钻石以上"
          value="{{formData.rank}}"
          bindinput="onRankInput"
        />
      </view>

      <view class="section-title">👤 客户信息</view>
      
      <view class="form-group">
        <text class="form-label">客户昵称 *</text>
        <input 
          class="form-input" 
          placeholder="请输入客户昵称"
          value="{{formData.clientName}}"
          bindinput="onClientNameInput"
        />
      </view>

      <view class="form-group">
        <text class="form-label">联系方式</text>
        <input 
          class="form-input" 
          placeholder="微信号、QQ号等"
          value="{{formData.clientContact}}"
          bindinput="onClientContactInput"
        />
      </view>

      <view class="section-title">💰 金额信息</view>
      
      <view class="form-group">
        <text class="form-label">订单金额 *</text>
        <view class="amount-input">
          <text class="amount-symbol">¥</text>
          <input 
            class="form-input" 
            type="digit" 
            placeholder="0.00"
            value="{{formData.totalAmount}}"
            bindinput="onTotalAmountInput"
          />
        </view>
      </view>

      <view class="amount-breakdown" wx:if="{{formData.totalAmount}}">
        <view class="breakdown-item">
          <text class="breakdown-label">我的收益:</text>
          <text class="breakdown-value positive">¥{{calculatedEarning}}</text>
        </view>
        <view class="breakdown-item">
          <text class="breakdown-label">俱乐部佣金:</text>
          <text class="breakdown-value">¥{{calculatedCommission}}</text>
        </view>
      </view>
    </view>

    <!-- 礼物单表单 -->
    <view class="form-section" wx:elif="{{orderType === 'gift'}}">
      <view class="section-title">🎁 礼物信息</view>
      
      <view class="form-group">
        <text class="form-label">礼物名称 *</text>
        <input 
          class="form-input" 
          placeholder="如：玫瑰花束"
          value="{{formData.giftName}}"
          bindinput="onGiftNameInput"
        />
      </view>

      <view class="form-group">
        <text class="form-label">礼物类型</text>
        <picker 
          mode="selector" 
          range="{{giftTypeOptions}}" 
          value="{{formData.giftTypeIndex}}"
          bindchange="onGiftTypeChange"
        >
          <view class="picker-input {{!formData.giftType ? 'placeholder' : ''}}">
            {{formData.giftType || '请选择礼物类型'}}
            <image class="picker-arrow" src="/assets/icons/arrow-down.png"></image>
          </view>
        </picker>
      </view>

      <view class="form-group">
        <text class="form-label">数量 *</text>
        <input 
          class="form-input" 
          type="number" 
          placeholder="1"
          value="{{formData.quantity}}"
          bindinput="onQuantityInput"
        />
      </view>

      <view class="form-group">
        <text class="form-label">单价 *</text>
        <view class="amount-input">
          <text class="amount-symbol">¥</text>
          <input 
            class="form-input" 
            type="digit" 
            placeholder="0.00"
            value="{{formData.unitPrice}}"
            bindinput="onUnitPriceInput"
          />
        </view>
      </view>

      <view class="section-title">👤 客户信息</view>
      
      <view class="form-group">
        <text class="form-label">客户昵称 *</text>
        <input 
          class="form-input" 
          placeholder="请输入客户昵称"
          value="{{formData.clientName}}"
          bindinput="onClientNameInput"
        />
      </view>

      <view class="amount-breakdown" wx:if="{{formData.unitPrice && formData.quantity}}">
        <view class="breakdown-item">
          <text class="breakdown-label">总金额:</text>
          <text class="breakdown-value">¥{{calculatedTotal}}</text>
        </view>
        <view class="breakdown-item">
          <text class="breakdown-label">我的收益:</text>
          <text class="breakdown-value positive">¥{{calculatedEarning}}</text>
        </view>
        <view class="breakdown-item">
          <text class="breakdown-label">俱乐部佣金:</text>
          <text class="breakdown-value">¥{{calculatedCommission}}</text>
        </view>
      </view>
    </view>

    <!-- 存单表单 -->
    <view class="form-section" wx:elif="{{orderType === 'deposit'}}">
      <view class="section-title">💳 存单信息</view>
      
      <view class="form-group">
        <text class="form-label">存款金额 *</text>
        <view class="amount-input">
          <text class="amount-symbol">¥</text>
          <input 
            class="form-input" 
            type="digit" 
            placeholder="0.00"
            value="{{formData.amount}}"
            bindinput="onAmountInput"
          />
        </view>
      </view>

      <view class="form-group">
        <text class="form-label">存款类型 *</text>
        <picker 
          mode="selector" 
          range="{{depositTypeOptions}}" 
          value="{{formData.depositTypeIndex}}"
          bindchange="onDepositTypeChange"
        >
          <view class="picker-input {{!formData.depositType ? 'placeholder' : ''}}">
            {{formData.depositType || '请选择存款类型'}}
            <image class="picker-arrow" src="/assets/icons/arrow-down.png"></image>
          </view>
        </picker>
      </view>

      <view class="form-group">
        <text class="form-label">存款用途 *</text>
        <input 
          class="form-input" 
          placeholder="如：保证金"
          value="{{formData.purpose}}"
          bindinput="onPurposeInput"
        />
      </view>

      <view class="form-group">
        <text class="form-label">到期日期</text>
        <picker 
          mode="date" 
          value="{{formData.maturityDate}}"
          bindchange="onMaturityDateChange"
        >
          <view class="picker-input {{!formData.maturityDate ? 'placeholder' : ''}}">
            {{formData.maturityDate || '请选择到期日期'}}
            <image class="picker-arrow" src="/assets/icons/arrow-down.png"></image>
          </view>
        </picker>
      </view>
    </view>

    <!-- 预存表单 -->
    <view class="form-section" wx:elif="{{orderType === 'predeposit'}}">
      <view class="section-title">💰 预存信息</view>
      
      <view class="form-group">
        <text class="form-label">预存金额 *</text>
        <view class="amount-input">
          <text class="amount-symbol">¥</text>
          <input 
            class="form-input" 
            type="digit" 
            placeholder="0.00"
            value="{{formData.amount}}"
            bindinput="onAmountInput"
          />
        </view>
      </view>

      <view class="form-group">
        <text class="form-label">预存用途 *</text>
        <input 
          class="form-input" 
          placeholder="如：游戏充值"
          value="{{formData.purpose}}"
          bindinput="onPurposeInput"
        />
      </view>

      <view class="form-group">
        <text class="form-label">游戏账号</text>
        <input 
          class="form-input" 
          placeholder="请输入游戏账号"
          value="{{formData.gameAccount}}"
          bindinput="onGameAccountInput"
        />
      </view>

      <view class="form-group">
        <text class="form-label">预计使用时间</text>
        <input 
          class="form-input" 
          placeholder="如：本周内"
          value="{{formData.expectedUsage}}"
          bindinput="onExpectedUsageInput"
        />
      </view>
    </view>

    <!-- 拉老板表单 -->
    <view class="form-section" wx:elif="{{orderType === 'client_acquisition'}}">
      <view class="section-title">👥 客户信息</view>
      
      <view class="form-group">
        <text class="form-label">客户昵称 *</text>
        <input 
          class="form-input" 
          placeholder="请输入客户昵称"
          value="{{formData.clientName}}"
          bindinput="onClientNameInput"
        />
      </view>

      <view class="form-group">
        <text class="form-label">客户联系方式 *</text>
        <input 
          class="form-input" 
          placeholder="微信号、QQ号等"
          value="{{formData.clientContact}}"
          bindinput="onClientContactInput"
        />
      </view>

      <view class="form-group">
        <text class="form-label">客户来源 *</text>
        <input 
          class="form-input" 
          placeholder="如：朋友介绍、广告等"
          value="{{formData.clientSource}}"
          bindinput="onClientSourceInput"
        />
      </view>

      <view class="form-group">
        <text class="form-label">游戏偏好</text>
        <input 
          class="form-input" 
          placeholder="客户喜欢的游戏类型"
          value="{{formData.gamePreference}}"
          bindinput="onGamePreferenceInput"
        />
      </view>

      <view class="form-group">
        <text class="form-label">预期收入</text>
        <view class="amount-input">
          <text class="amount-symbol">¥</text>
          <input 
            class="form-input" 
            type="digit" 
            placeholder="0.00"
            value="{{formData.expectedRevenue}}"
            bindinput="onExpectedRevenueInput"
          />
        </view>
      </view>
    </view>

    <!-- 通用字段 -->
    <view class="form-section">
      <view class="section-title">📝 备注信息</view>
      
      <view class="form-group">
        <text class="form-label">备注</text>
        <textarea 
          class="form-textarea" 
          placeholder="可选填写备注信息..."
          value="{{formData.notes}}"
          bindinput="onNotesInput"
          maxlength="200"
        ></textarea>
      </view>

      <view class="form-group">
        <text class="form-label">上传图片</text>
        <view class="image-upload">
          <view 
            class="upload-item"
            wx:for="{{formData.images}}"
            wx:key="index"
          >
            <image class="upload-image" src="{{item}}" mode="aspectFill"></image>
            <view class="delete-btn" bindtap="deleteImage" data-index="{{index}}">×</view>
          </view>
          
          <view class="upload-btn" bindtap="chooseImage" wx:if="{{formData.images.length < 3}}">
            <image class="upload-icon" src="/assets/icons/camera.png"></image>
            <text class="upload-text">添加图片</text>
          </view>
        </view>
        <text class="upload-tip">最多上传3张图片</text>
      </view>
    </view>

    <!-- 提交按钮 -->
    <view class="submit-section">
      <button class="submit-btn" form-type="submit" loading="{{submitting}}">
        {{submitting ? '提交中...' : (isEdit ? '更新' : '提交审核')}}
      </button>
      
      <button class="cancel-btn" bindtap="onCancel" wx:if="{{!submitting}}">
        取消
      </button>
    </view>
  </form>
</view>
