{"description": "陪玩服务管理系统", "packOptions": {"ignore": [{"type": "file", "value": ".eslintrc.js"}, {"type": "file", "value": ".giti<PERSON>re"}, {"type": "file", "value": "README.md"}, {"type": "folder", "value": "docs"}]}, "setting": {"urlCheck": false, "es6": true, "enhance": true, "postcss": true, "preloadBackgroundData": false, "minified": true, "newFeature": false, "coverView": true, "nodeModules": false, "autoAudits": false, "showShadowRootInWxmlPanel": true, "scopeDataCheck": false, "uglifyFileName": false, "checkInvalidKey": true, "checkSiteMap": true, "uploadWithSourceMap": true, "compileHotReLoad": false, "lazyloadPlaceholderEnable": false, "useMultiFrameRuntime": true, "useApiHook": true, "useApiHostProcess": true, "babelSetting": {"ignore": [], "disablePlugins": [], "outputPath": ""}, "enableEngineNative": false, "useIsolateContext": false, "userConfirmedBundleSwitch": false, "packNpmManually": false, "packNpmRelationList": [], "minifyWXSS": true, "disableUseStrict": false, "minifyWXML": true, "showES6CompileOption": false, "useCompilerPlugins": false}, "compileType": "miniprogram", "libVersion": "2.19.4", "appid": "your-app-id-here", "projectname": "companion-gaming-system", "debugOptions": {"hidedInDevtools": []}, "scripts": {}, "staticServerOptions": {"baseURL": "", "servePath": ""}, "isGameTourist": false, "cloudfunctionTemplateRoot": "cloudfunctionTemplate", "cloudfunctionRoot": "cloudfunctions/", "condition": {"search": {"list": []}, "conversation": {"list": []}, "game": {"list": []}, "plugin": {"list": []}, "gamePlugin": {"list": []}, "miniprogram": {"list": [{"name": "登录页面", "pathName": "pages/login/login", "query": "", "scene": null}, {"name": "陪玩师首页", "pathName": "pages/companion/home/<USER>", "query": "", "scene": null}, {"name": "管理员首页", "pathName": "pages/manager/home/<USER>", "query": "", "scene": null}, {"name": "报单管理", "pathName": "pages/companion/orders/orders", "query": "", "scene": null}, {"name": "创建订单", "pathName": "pages/companion/create-order/create-order", "query": "type=gaming", "scene": null}]}}}