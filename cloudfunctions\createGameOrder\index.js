// 云函数入口文件
const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()

// 云函数入口函数
exports.main = async (event, context) => {
  const wxContext = cloud.getWXContext()
  
  try {
    const { gameOrderData } = event
    const openid = wxContext.OPENID
    
    // 获取用户信息
    const userQuery = await db.collection('users').where({
      openid: openid
    }).get()
    
    if (userQuery.data.length === 0) {
      return {
        success: false,
        message: '用户不存在'
      }
    }
    
    const user = userQuery.data[0]
    
    // 验证用户是否是俱乐部成员
    const memberQuery = await db.collection('club_members').where({
      userId: user._id,
      clubId: gameOrderData.clubId,
      status: 'active'
    }).get()
    
    if (memberQuery.data.length === 0) {
      return {
        success: false,
        message: '您不是该俱乐部的成员'
      }
    }
    
    // 获取俱乐部信息以计算佣金
    const clubQuery = await db.collection('clubs').doc(gameOrderData.clubId).get()
    if (!clubQuery.data) {
      return {
        success: false,
        message: '俱乐部不存在'
      }
    }
    
    const club = clubQuery.data
    const commissionRate = club.settings?.commissionRate || 0.15
    
    // 计算金额分配
    const totalAmount = parseFloat(gameOrderData.totalAmount) || 0
    const clubCommission = totalAmount * commissionRate
    const companionEarning = totalAmount - clubCommission
    
    // 生成订单号
    const orderNumber = 'G' + Date.now().toString().slice(-8)
    
    // 创建游戏订单
    const orderData = {
      orderNumber: orderNumber,
      clubId: gameOrderData.clubId,
      companionId: user._id,
      orderType: 'gaming',
      gameInfo: {
        gameName: gameOrderData.gameName,
        gameMode: gameOrderData.gameMode,
        duration: parseInt(gameOrderData.duration) || 0,
        rank: gameOrderData.rank || ''
      },
      clientInfo: {
        clientName: gameOrderData.clientName,
        clientContact: gameOrderData.clientContact || ''
      },
      financial: {
        totalAmount: totalAmount,
        companionEarning: companionEarning,
        clubCommission: clubCommission,
        platformFee: 0
      },
      status: 'pending',
      startTime: gameOrderData.startTime || null,
      endTime: gameOrderData.endTime || null,
      notes: gameOrderData.notes || '',
      attachments: gameOrderData.attachments || [],
      createdAt: new Date(),
      updatedAt: new Date()
    }
    
    const result = await db.collection('game_orders').add({
      data: orderData
    })
    
    return {
      success: true,
      data: {
        orderId: result._id,
        orderNumber: orderNumber
      },
      message: '游戏单创建成功'
    }
    
  } catch (error) {
    console.error('创建游戏单失败:', error)
    return {
      success: false,
      message: '创建失败: ' + error.message
    }
  }
}
