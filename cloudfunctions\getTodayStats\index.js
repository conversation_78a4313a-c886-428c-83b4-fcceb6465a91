// 云函数入口文件
const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()
const _ = db.command

// 云函数入口函数
exports.main = async (event, context) => {
  const wxContext = cloud.getWXContext()
  
  try {
    const { clubId } = event
    const openid = wxContext.OPENID
    
    // 获取用户信息
    const userQuery = await db.collection('users').where({
      openid: openid
    }).get()
    
    if (userQuery.data.length === 0) {
      return {
        success: false,
        message: '用户不存在'
      }
    }
    
    const user = userQuery.data[0]
    
    // 验证用户权限
    const memberQuery = await db.collection('club_members').where({
      userId: user._id,
      clubId: clubId,
      status: 'active'
    }).get()
    
    if (memberQuery.data.length === 0) {
      return {
        success: false,
        message: '您不是该俱乐部的成员'
      }
    }
    
    const member = memberQuery.data[0]
    
    // 获取今天的开始和结束时间
    const today = new Date()
    const startOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate())
    const endOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate() + 1)
    
    // 构建查询条件
    let whereCondition = {
      clubId: clubId,
      createdAt: _.gte(startOfDay).and(_.lt(endOfDay))
    }
    
    // 如果是普通陪玩师，只统计自己的数据
    if (member.role === 'member') {
      whereCondition.companionId = user._id
    }
    
    // 统计各类订单
    const collections = [
      { name: 'game_orders', type: 'gaming' },
      { name: 'gift_orders', type: 'gift' },
      { name: 'fine_records', type: 'fine' },
      { name: 'group_fee_records', type: 'group_fee' },
      { name: 'deposit_records', type: 'deposit' },
      { name: 'predeposit_records', type: 'predeposit' },
      { name: 'client_acquisition_records', type: 'client_acquisition' }
    ]
    
    let gameOrders = 0
    let giftOrders = 0
    let totalEarnings = 0
    let pendingAmount = 0
    
    for (const collection of collections) {
      try {
        const result = await db.collection(collection.name).where(whereCondition).get()
        
        for (const order of result.data) {
          // 统计订单数量
          if (collection.type === 'gaming') {
            gameOrders++
          } else if (collection.type === 'gift') {
            giftOrders++
          }
          
          // 计算收益
          let earning = 0
          let isPending = order.status === 'pending'
          
          switch (collection.type) {
            case 'gaming':
            case 'gift':
              earning = order.financial?.companionEarning || 0
              break
            case 'fine':
              earning = -(order.fineInfo?.amount || 0)
              break
            case 'group_fee':
              earning = -(order.feeInfo?.amount || 0)
              break
            case 'deposit':
              earning = order.depositInfo?.amount || 0
              break
            case 'predeposit':
              earning = order.predepositInfo?.amount || 0
              break
            case 'client_acquisition':
              earning = order.reward?.amount || 0
              break
          }
          
          if (isPending) {
            pendingAmount += earning
          } else if (order.status === 'approved' || order.status === 'completed') {
            totalEarnings += earning
          }
        }
      } catch (error) {
        console.warn(`查询 ${collection.name} 失败:`, error)
      }
    }
    
    return {
      success: true,
      data: {
        gameOrders: gameOrders,
        giftOrders: giftOrders,
        totalEarnings: Math.max(0, totalEarnings),
        pendingAmount: Math.max(0, pendingAmount)
      },
      message: '获取成功'
    }
    
  } catch (error) {
    console.error('获取今日统计失败:', error)
    return {
      success: false,
      message: '获取失败: ' + error.message
    }
  }
}
