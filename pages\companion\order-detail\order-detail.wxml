<!--pages/companion/order-detail/order-detail.wxml-->
<view class="container">
  <!-- 订单状态 -->
  <view class="status-section">
    <view class="status-badge status-{{orderDetail.status}}">
      {{orderDetail.statusText}}
    </view>
    <text class="order-number">#{{orderDetail.orderNumber}}</text>
  </view>

  <!-- 订单基本信息 -->
  <view class="card">
    <view class="card-title">{{orderDetail.typeText}}</view>
    
    <!-- 游戏单信息 -->
    <view wx:if="{{orderDetail.orderType === 'gaming'}}">
      <view class="info-item">
        <text class="label">游戏名称:</text>
        <text class="value">{{orderDetail.gameInfo.gameName}}</text>
      </view>
      <view class="info-item">
        <text class="label">游戏模式:</text>
        <text class="value">{{orderDetail.gameInfo.gameMode}}</text>
      </view>
      <view class="info-item">
        <text class="label">游戏时长:</text>
        <text class="value">{{orderDetail.gameInfo.duration}}分钟</text>
      </view>
      <view class="info-item" wx:if="{{orderDetail.gameInfo.rank}}">
        <text class="label">段位要求:</text>
        <text class="value">{{orderDetail.gameInfo.rank}}</text>
      </view>
    </view>

    <!-- 礼物单信息 -->
    <view wx:elif="{{orderDetail.orderType === 'gift'}}">
      <view class="info-item">
        <text class="label">礼物名称:</text>
        <text class="value">{{orderDetail.giftInfo.giftName}}</text>
      </view>
      <view class="info-item">
        <text class="label">礼物类型:</text>
        <text class="value">{{orderDetail.giftInfo.giftType}}</text>
      </view>
      <view class="info-item">
        <text class="label">数量:</text>
        <text class="value">{{orderDetail.giftInfo.quantity}}</text>
      </view>
      <view class="info-item">
        <text class="label">单价:</text>
        <text class="value">¥{{orderDetail.giftInfo.unitPrice}}</text>
      </view>
    </view>

    <!-- 客户信息 -->
    <view wx:if="{{orderDetail.clientInfo}}">
      <view class="section-divider"></view>
      <view class="info-item">
        <text class="label">客户昵称:</text>
        <text class="value">{{orderDetail.clientInfo.clientName}}</text>
      </view>
      <view class="info-item" wx:if="{{orderDetail.clientInfo.clientContact}}">
        <text class="label">联系方式:</text>
        <text class="value">{{orderDetail.clientInfo.clientContact}}</text>
      </view>
    </view>
  </view>

  <!-- 金额信息 -->
  <view class="card" wx:if="{{orderDetail.financial}}">
    <view class="card-title">💰 金额信息</view>
    
    <view class="amount-item">
      <text class="amount-label">订单总额:</text>
      <text class="amount-value">¥{{orderDetail.financial.totalAmount}}</text>
    </view>
    
    <view class="amount-item">
      <text class="amount-label">我的收益:</text>
      <text class="amount-value positive">¥{{orderDetail.financial.companionEarning}}</text>
    </view>
    
    <view class="amount-item">
      <text class="amount-label">俱乐部佣金:</text>
      <text class="amount-value">¥{{orderDetail.financial.clubCommission}}</text>
    </view>
  </view>

  <!-- 时间信息 -->
  <view class="card">
    <view class="card-title">⏰ 时间信息</view>
    
    <view class="info-item">
      <text class="label">创建时间:</text>
      <text class="value">{{orderDetail.createdAt}}</text>
    </view>
    
    <view class="info-item" wx:if="{{orderDetail.startTime}}">
      <text class="label">开始时间:</text>
      <text class="value">{{orderDetail.startTime}}</text>
    </view>
    
    <view class="info-item" wx:if="{{orderDetail.endTime}}">
      <text class="label">结束时间:</text>
      <text class="value">{{orderDetail.endTime}}</text>
    </view>
  </view>

  <!-- 备注信息 -->
  <view class="card" wx:if="{{orderDetail.notes}}">
    <view class="card-title">📝 备注信息</view>
    <text class="notes-text">{{orderDetail.notes}}</text>
  </view>

  <!-- 附件图片 -->
  <view class="card" wx:if="{{orderDetail.attachments && orderDetail.attachments.length > 0}}">
    <view class="card-title">📷 附件图片</view>
    <view class="image-list">
      <image 
        class="attachment-image"
        wx:for="{{orderDetail.attachments}}"
        wx:key="index"
        src="{{item}}"
        mode="aspectFill"
        bindtap="previewImage"
        data-url="{{item}}"
        data-urls="{{orderDetail.attachments}}"
      ></image>
    </view>
  </view>

  <!-- 审核信息 -->
  <view class="card" wx:if="{{orderDetail.reviewInfo}}">
    <view class="card-title">✅ 审核信息</view>
    
    <view class="info-item">
      <text class="label">审核人:</text>
      <text class="value">{{orderDetail.reviewInfo.reviewerName}}</text>
    </view>
    
    <view class="info-item">
      <text class="label">审核时间:</text>
      <text class="value">{{orderDetail.reviewInfo.reviewedAt}}</text>
    </view>
    
    <view class="info-item" wx:if="{{orderDetail.reviewInfo.reviewNote}}">
      <text class="label">审核备注:</text>
      <text class="value">{{orderDetail.reviewInfo.reviewNote}}</text>
    </view>
  </view>

  <!-- 操作按钮 -->
  <view class="action-section" wx:if="{{showActions}}">
    <button 
      class="action-btn btn-secondary" 
      bindtap="editOrder"
      wx:if="{{orderDetail.status === 'pending'}}"
    >
      编辑订单
    </button>
    
    <button 
      class="action-btn btn-danger" 
      bindtap="deleteOrder"
      wx:if="{{orderDetail.status === 'pending'}}"
    >
      删除订单
    </button>
    
    <button 
      class="action-btn btn-primary" 
      bindtap="resubmitOrder"
      wx:if="{{orderDetail.status === 'rejected'}}"
    >
      重新提交
    </button>
  </view>
</view>
