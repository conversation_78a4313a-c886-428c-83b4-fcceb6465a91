// 云函数入口文件
const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()
const _ = db.command

// 云函数入口函数
exports.main = async (event, context) => {
  const wxContext = cloud.getWXContext()
  
  try {
    const { clubId, filters = {}, page = 1, pageSize = 20 } = event
    const openid = wxContext.OPENID
    
    // 获取用户信息
    const userQuery = await db.collection('users').where({
      openid: openid
    }).get()
    
    if (userQuery.data.length === 0) {
      return {
        success: false,
        message: '用户不存在'
      }
    }
    
    const user = userQuery.data[0]
    
    // 验证用户权限
    const memberQuery = await db.collection('club_members').where({
      userId: user._id,
      clubId: clubId,
      status: 'active'
    }).get()
    
    if (memberQuery.data.length === 0) {
      return {
        success: false,
        message: '您不是该俱乐部的成员'
      }
    }
    
    const member = memberQuery.data[0]
    
    // 构建查询条件
    let whereCondition = {
      clubId: clubId
    }
    
    // 如果是普通陪玩师，只能查看自己的订单
    if (member.role === 'member') {
      whereCondition.companionId = user._id
    }
    
    // 添加筛选条件
    if (filters.status) {
      whereCondition.status = filters.status
    }
    
    if (filters.orderType) {
      whereCondition.orderType = filters.orderType
    }
    
    if (filters.keyword) {
      // 关键词搜索（这里简化处理，实际可能需要更复杂的搜索逻辑）
      whereCondition = _.or([
        { ...whereCondition, 'gameInfo.gameName': new RegExp(filters.keyword, 'i') },
        { ...whereCondition, 'giftInfo.giftName': new RegExp(filters.keyword, 'i') },
        { ...whereCondition, 'clientInfo.clientName': new RegExp(filters.keyword, 'i') }
      ])
    }
    
    // 计算跳过的记录数
    const skip = (page - 1) * pageSize
    
    // 查询所有类型的订单
    const collections = ['game_orders', 'gift_orders', 'fine_records', 'group_fee_records', 
                        'deposit_records', 'predeposit_records', 'client_acquisition_records']
    
    let allOrders = []
    
    for (const collectionName of collections) {
      try {
        const query = db.collection(collectionName).where(whereCondition)
        const result = await query.orderBy('createdAt', 'desc').get()
        
        // 为每个订单添加类型标识
        const ordersWithType = result.data.map(order => ({
          ...order,
          orderType: getOrderTypeFromCollection(collectionName),
          collection: collectionName
        }))
        
        allOrders = allOrders.concat(ordersWithType)
      } catch (error) {
        console.warn(`查询 ${collectionName} 失败:`, error)
      }
    }
    
    // 按创建时间排序
    allOrders.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt))
    
    // 分页处理
    const totalCount = allOrders.length
    const paginatedOrders = allOrders.slice(skip, skip + pageSize)
    
    return {
      success: true,
      data: {
        orders: paginatedOrders,
        totalCount: totalCount,
        page: page,
        pageSize: pageSize,
        hasMore: skip + pageSize < totalCount
      },
      message: '获取成功'
    }
    
  } catch (error) {
    console.error('获取订单失败:', error)
    return {
      success: false,
      message: '获取失败: ' + error.message
    }
  }
}

// 根据集合名称获取订单类型
function getOrderTypeFromCollection(collectionName) {
  const typeMap = {
    'game_orders': 'gaming',
    'gift_orders': 'gift',
    'fine_records': 'fine',
    'group_fee_records': 'group_fee',
    'deposit_records': 'deposit',
    'predeposit_records': 'predeposit',
    'client_acquisition_records': 'client_acquisition'
  }
  return typeMap[collectionName] || 'unknown'
}
