/* pages/companion/earnings/earnings.wxss */

.container {
  background-color: #f8f8f8;
  min-height: 100vh;
}

/* 筛选区域 */
.filter-section {
  background-color: #fff;
  padding: 20rpx 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.time-filter {
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.filter-btn {
  display: flex;
  align-items: center;
  padding: 16rpx 24rpx;
  background-color: #f8f9fa;
  border-radius: 8rpx;
  font-size: 28rpx;
  color: #333;
}

.filter-arrow {
  width: 24rpx;
  height: 24rpx;
  margin-left: 8rpx;
}

.custom-date {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.date-input {
  padding: 16rpx 20rpx;
  background-color: #f8f9fa;
  border-radius: 8rpx;
  font-size: 26rpx;
  color: #333;
}

.date-separator {
  font-size: 24rpx;
  color: #666;
}

/* 收益概览 */
.summary-section {
  padding: 20rpx 30rpx;
}

.summary-card {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.summary-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 30rpx;
}

.summary-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16rpx 0;
}

.summary-item.main {
  padding: 24rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
  margin-bottom: 20rpx;
}

.summary-label {
  font-size: 28rpx;
  color: #666;
}

.summary-item.main .summary-label {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.summary-value {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.summary-item.main .summary-value {
  font-size: 48rpx;
}

.summary-value.positive {
  color: #1AAD19;
}

.summary-value.negative {
  color: #FF6B6B;
}

.summary-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20rpx 0;
}

/* 图表区域 */
.chart-section {
  padding: 0 30rpx 20rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 20rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.more {
  font-size: 24rpx;
  color: #1AAD19;
}

.chart-container {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.chart-placeholder {
  height: 200rpx;
  background-color: #f8f9fa;
  border-radius: 12rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.chart-text {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 16rpx;
}

.trend-info {
  display: flex;
  gap: 40rpx;
}

.trend-item {
  font-size: 24rpx;
  color: #999;
}

/* 收益构成 */
.composition-section {
  padding: 0 30rpx 20rpx;
}

.composition-chart {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.pie-chart-placeholder {
  height: 200rpx;
  background-color: #f8f9fa;
  border-radius: 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 30rpx;
}

.composition-legend {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.legend-color {
  width: 24rpx;
  height: 24rpx;
  border-radius: 4rpx;
}

.legend-color.game {
  background-color: #1AAD19;
}

.legend-color.gift {
  background-color: #FF6B6B;
}

.legend-color.other {
  background-color: #4ECDC4;
}

.legend-text {
  font-size: 26rpx;
  color: #333;
}

/* 排名信息 */
.ranking-section {
  padding: 0 30rpx 20rpx;
}

.ranking-card {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.ranking-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.ranking-item:last-child {
  border-bottom: none;
}

.ranking-label {
  font-size: 28rpx;
  color: #666;
}

.ranking-value {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
}

/* 详细记录 */
.records-section {
  padding: 0 30rpx 20rpx;
}

.record-list {
  background-color: #fff;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.record-item {
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.record-item:last-child {
  border-bottom: none;
}

.record-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12rpx;
}

.record-type {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
}

.record-date {
  font-size: 24rpx;
  color: #999;
}

.record-desc {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 16rpx;
  display: block;
}

.record-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.record-status {
  font-size: 22rpx;
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  color: #fff;
}

.status-pending {
  background-color: #FF9500;
}

.status-approved {
  background-color: #1AAD19;
}

.status-completed {
  background-color: #1AAD19;
}

.record-amount {
  font-size: 28rpx;
  font-weight: 600;
}

.record-amount.positive {
  color: #1AAD19;
}

.record-amount.negative {
  color: #FF6B6B;
}

.record-amount.normal {
  color: #333;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 60rpx 40rpx;
  background-color: #fff;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.empty-text {
  font-size: 28rpx;
  color: #999;
}
