<!--pages/companion/profile/profile.wxml-->
<view class="container">
  <!-- 用户信息 -->
  <view class="profile-section">
    <view class="user-card">
      <image class="avatar" src="{{userInfo.avatarUrl}}" mode="aspectFill"></image>
      <view class="user-details">
        <text class="nickname">{{userInfo.nickName}}</text>
        <text class="role">陪玩师</text>
        <text class="join-date">加入时间: {{joinDate}}</text>
      </view>
      <view class="edit-btn" bindtap="editProfile">
        <image class="edit-icon" src="/assets/icons/edit.png"></image>
      </view>
    </view>
  </view>

  <!-- 统计信息 -->
  <view class="stats-section">
    <view class="stats-grid">
      <view class="stats-item">
        <text class="stats-number">{{userStats.totalOrders}}</text>
        <text class="stats-label">总订单</text>
      </view>
      
      <view class="stats-item">
        <text class="stats-number">{{userStats.totalEarnings}}</text>
        <text class="stats-label">总收益</text>
      </view>
      
      <view class="stats-item">
        <text class="stats-number">{{userStats.clubCount}}</text>
        <text class="stats-label">加入俱乐部</text>
      </view>
      
      <view class="stats-item">
        <text class="stats-number">{{userStats.rating}}</text>
        <text class="stats-label">评分</text>
      </view>
    </view>
  </view>

  <!-- 功能菜单 -->
  <view class="menu-section">
    <view class="menu-group">
      <view class="menu-item" bindtap="viewClubs">
        <view class="menu-icon">
          <image src="/assets/icons/clubs.png"></image>
        </view>
        <text class="menu-text">我的俱乐部</text>
        <view class="menu-arrow">
          <image src="/assets/icons/arrow-right.png"></image>
        </view>
      </view>
      
      <view class="menu-item" bindtap="viewEarnings">
        <view class="menu-icon">
          <image src="/assets/icons/earnings.png"></image>
        </view>
        <text class="menu-text">收益统计</text>
        <view class="menu-arrow">
          <image src="/assets/icons/arrow-right.png"></image>
        </view>
      </view>
      
      <view class="menu-item" bindtap="viewOrders">
        <view class="menu-icon">
          <image src="/assets/icons/orders.png"></image>
        </view>
        <text class="menu-text">我的报单</text>
        <view class="menu-arrow">
          <image src="/assets/icons/arrow-right.png"></image>
        </view>
      </view>
    </view>

    <view class="menu-group">
      <view class="menu-item" bindtap="viewNotifications">
        <view class="menu-icon">
          <image src="/assets/icons/notification.png"></image>
        </view>
        <text class="menu-text">消息通知</text>
        <view class="menu-badge" wx:if="{{unreadCount > 0}}">{{unreadCount}}</view>
        <view class="menu-arrow">
          <image src="/assets/icons/arrow-right.png"></image>
        </view>
      </view>
      
      <view class="menu-item" bindtap="viewSettings">
        <view class="menu-icon">
          <image src="/assets/icons/settings.png"></image>
        </view>
        <text class="menu-text">设置</text>
        <view class="menu-arrow">
          <image src="/assets/icons/arrow-right.png"></image>
        </view>
      </view>
      
      <view class="menu-item" bindtap="viewHelp">
        <view class="menu-icon">
          <image src="/assets/icons/help.png"></image>
        </view>
        <text class="menu-text">帮助与反馈</text>
        <view class="menu-arrow">
          <image src="/assets/icons/arrow-right.png"></image>
        </view>
      </view>
    </view>

    <view class="menu-group">
      <view class="menu-item" bindtap="viewAbout">
        <view class="menu-icon">
          <image src="/assets/icons/about.png"></image>
        </view>
        <text class="menu-text">关于我们</text>
        <view class="menu-arrow">
          <image src="/assets/icons/arrow-right.png"></image>
        </view>
      </view>
    </view>
  </view>

  <!-- 退出登录 -->
  <view class="logout-section">
    <button class="logout-btn" bindtap="logout">退出登录</button>
  </view>

  <!-- 版本信息 -->
  <view class="version-section">
    <text class="version-text">版本 {{version}}</text>
  </view>
</view>

<!-- 编辑资料弹窗 -->
<view class="edit-modal" wx:if="{{showEditModal}}" bindtap="hideEditModal">
  <view class="modal-content" catchtap="stopPropagation">
    <view class="modal-header">
      <text class="modal-title">编辑资料</text>
      <image class="close-icon" src="/assets/icons/close.png" bindtap="hideEditModal"></image>
    </view>
    
    <view class="modal-body">
      <view class="form-group">
        <text class="form-label">真实姓名</text>
        <input 
          class="form-input" 
          placeholder="请输入真实姓名"
          value="{{editForm.realName}}"
          bindinput="onRealNameInput"
        />
      </view>
      
      <view class="form-group">
        <text class="form-label">手机号码</text>
        <input 
          class="form-input" 
          type="number"
          placeholder="请输入手机号码"
          value="{{editForm.phone}}"
          bindinput="onPhoneInput"
        />
      </view>
      
      <view class="form-group">
        <text class="form-label">微信号</text>
        <input 
          class="form-input" 
          placeholder="请输入微信号"
          value="{{editForm.wechatId}}"
          bindinput="onWechatIdInput"
        />
      </view>
    </view>
    
    <view class="modal-footer">
      <button class="modal-btn cancel" bindtap="hideEditModal">取消</button>
      <button class="modal-btn confirm" bindtap="saveProfile" loading="{{saving}}">
        {{saving ? '保存中...' : '保存'}}
      </button>
    </view>
  </view>
</view>
