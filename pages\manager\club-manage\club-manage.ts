// pages/manager/club-manage/club-manage.ts
import api from '../../../utils/api'
import { formatDate, showError, showSuccess, showConfirm } from '../../../utils/util'

const app = getApp<IAppOption>()

Page({
  data: {
    clubInfo: {} as any,
    clubStats: {
      memberCount: 0,
      totalOrders: 0,
      totalRevenue: '0.00',
      totalCommission: '0.00'
    },
    clubSettings: {
      maxMembers: 100,
      commissionRate: 15,
      autoApprove: false,
      allowMultipleClubs: true,
      workingHours: {
        start: '09:00',
        end: '23:00'
      }
    },
    inviteCode: '',
    
    // 编辑相关
    showEditModal: false,
    editType: '',
    editModalTitle: '',
    editForm: {} as any,
    editing: false,
    saving: false
  },

  onLoad() {
    this.loadClubInfo()
  },

  onShow() {
    this.loadClubStats()
  },

  // 加载俱乐部信息
  async loadClubInfo() {
    const currentClub = app.globalData.currentClub
    if (!currentClub) {
      // 如果没有俱乐部，显示创建界面
      this.showCreateClubDialog()
      return
    }

    try {
      const res = await api.getClubInfo(currentClub.id)
      if (res.success) {
        const clubInfo = {
          ...res.data.club,
          statusText: this.getClubStatusText(res.data.club.status),
          createdAt: formatDate(new Date(res.data.club.createdAt))
        }
        
        this.setData({ 
          clubInfo,
          clubSettings: {
            ...this.data.clubSettings,
            ...res.data.club.settings
          },
          inviteCode: res.data.club.inviteCode || this.generateInviteCode()
        })
      }
    } catch (error) {
      console.error('加载俱乐部信息失败:', error)
      showError('加载俱乐部信息失败')
    }
  },

  // 加载俱乐部统计
  async loadClubStats() {
    const currentClub = app.globalData.currentClub
    if (!currentClub) return

    try {
      const res = await api.callFunction('getClubStats', {
        clubId: currentClub.id
      }, false)
      
      if (res.success) {
        this.setData({
          clubStats: {
            memberCount: res.data.memberCount || 0,
            totalOrders: res.data.totalOrders || 0,
            totalRevenue: (res.data.totalRevenue || 0).toFixed(2),
            totalCommission: (res.data.totalCommission || 0).toFixed(2)
          }
        })
      }
    } catch (error) {
      console.error('加载俱乐部统计失败:', error)
    }
  },

  // 显示创建俱乐部对话框
  showCreateClubDialog() {
    wx.showModal({
      title: '创建俱乐部',
      content: '您还没有俱乐部，是否立即创建？',
      success: (res) => {
        if (res.confirm) {
          this.createClub()
        } else {
          wx.navigateBack()
        }
      }
    })
  },

  // 创建俱乐部
  async createClub() {
    wx.showModal({
      title: '创建俱乐部',
      content: '请输入俱乐部名称',
      editable: true,
      placeholderText: '俱乐部名称',
      success: async (res) => {
        if (res.confirm && res.content) {
          try {
            const result = await api.createClub({
              name: res.content,
              description: '',
              settings: this.data.clubSettings
            })
            
            if (result.success) {
              showSuccess('俱乐部创建成功')
              // 重新加载用户俱乐部
              const clubsRes = await api.getUserClubs()
              if (clubsRes.success) {
                app.globalData.clubs = clubsRes.data.clubs
                app.globalData.currentClub = clubsRes.data.clubs[0]
                this.loadClubInfo()
              }
            }
          } catch (error) {
            console.error('创建俱乐部失败:', error)
            showError('创建俱乐部失败')
          }
        }
      }
    })
  },

  // 编辑俱乐部信息
  editClubInfo() {
    this.setData({
      showEditModal: true,
      editType: 'clubInfo',
      editModalTitle: '编辑俱乐部信息',
      editForm: {
        name: this.data.clubInfo.name || '',
        description: this.data.clubInfo.description || ''
      }
    })
  },

  // 编辑最大成员数
  editMaxMembers() {
    this.setData({
      showEditModal: true,
      editType: 'maxMembers',
      editModalTitle: '设置最大成员数',
      editForm: {
        maxMembers: this.data.clubSettings.maxMembers.toString()
      }
    })
  },

  // 编辑佣金比例
  editCommissionRate() {
    this.setData({
      showEditModal: true,
      editType: 'commissionRate',
      editModalTitle: '设置佣金比例',
      editForm: {
        commissionRate: this.data.clubSettings.commissionRate.toString()
      }
    })
  },

  // 隐藏编辑弹窗
  hideEditModal() {
    this.setData({ showEditModal: false })
  },

  // 阻止事件冒泡
  stopPropagation() {},

  // 输入事件
  onNameInput(e: any) {
    this.setData({ 'editForm.name': e.detail.value })
  },

  onDescriptionInput(e: any) {
    this.setData({ 'editForm.description': e.detail.value })
  },

  onMaxMembersInput(e: any) {
    this.setData({ 'editForm.maxMembers': e.detail.value })
  },

  onCommissionRateInput(e: any) {
    this.setData({ 'editForm.commissionRate': e.detail.value })
  },

  // 确认编辑
  async confirmEdit() {
    this.setData({ editing: true })

    try {
      let updateData: any = {}
      
      switch (this.data.editType) {
        case 'clubInfo':
          updateData = {
            name: this.data.editForm.name,
            description: this.data.editForm.description
          }
          break
        case 'maxMembers':
          updateData = {
            settings: {
              ...this.data.clubSettings,
              maxMembers: parseInt(this.data.editForm.maxMembers)
            }
          }
          break
        case 'commissionRate':
          const rate = parseFloat(this.data.editForm.commissionRate)
          if (rate < 0 || rate > 50) {
            showError('佣金比例应在0-50%之间')
            return
          }
          updateData = {
            settings: {
              ...this.data.clubSettings,
              commissionRate: rate
            }
          }
          break
      }

      const res = await api.updateClub(this.data.clubInfo.id, updateData)
      if (res.success) {
        showSuccess('保存成功')
        this.hideEditModal()
        this.loadClubInfo()
      }
    } catch (error) {
      console.error('保存失败:', error)
      showError('保存失败')
    } finally {
      this.setData({ editing: false })
    }
  },

  // 开关变化
  onAutoApproveChange(e: any) {
    this.setData({ 'clubSettings.autoApprove': e.detail.value })
  },

  onAllowMultipleChange(e: any) {
    this.setData({ 'clubSettings.allowMultipleClubs': e.detail.value })
  },

  // 时间选择
  onStartTimeChange(e: any) {
    this.setData({ 'clubSettings.workingHours.start': e.detail.value })
  },

  onEndTimeChange(e: any) {
    this.setData({ 'clubSettings.workingHours.end': e.detail.value })
  },

  // 复制邀请码
  copyInviteCode() {
    wx.setClipboardData({
      data: this.data.inviteCode,
      success: () => {
        showSuccess('邀请码已复制')
      }
    })
  },

  // 刷新邀请码
  async refreshInviteCode() {
    const confirmed = await showConfirm('确定要重新生成邀请码吗？原邀请码将失效。')
    if (!confirmed) return

    try {
      const newCode = this.generateInviteCode()
      const res = await api.updateClub(this.data.clubInfo.id, {
        inviteCode: newCode
      })
      
      if (res.success) {
        this.setData({ inviteCode: newCode })
        showSuccess('邀请码已更新')
      }
    } catch (error) {
      console.error('更新邀请码失败:', error)
      showError('更新邀请码失败')
    }
  },

  // 生成邀请码
  generateInviteCode() {
    return Math.random().toString(36).substr(2, 8).toUpperCase()
  },

  // 保存设置
  async saveSettings() {
    this.setData({ saving: true })

    try {
      const res = await api.updateClub(this.data.clubInfo.id, {
        settings: this.data.clubSettings
      })
      
      if (res.success) {
        showSuccess('设置已保存')
      }
    } catch (error) {
      console.error('保存设置失败:', error)
      showError('保存设置失败')
    } finally {
      this.setData({ saving: false })
    }
  },

  // 转让俱乐部
  transferOwnership() {
    showError('功能开发中')
  },

  // 解散俱乐部
  async dissolveClub() {
    const confirmed = await showConfirm('确定要解散俱乐部吗？此操作不可恢复！', '危险操作')
    if (!confirmed) return

    try {
      const res = await api.callFunction('dissolveClub', {
        clubId: this.data.clubInfo.id
      })
      
      if (res.success) {
        showSuccess('俱乐部已解散')
        wx.reLaunch({
          url: '/pages/login/login'
        })
      }
    } catch (error) {
      console.error('解散俱乐部失败:', error)
      showError('解散俱乐部失败')
    }
  },

  // 获取俱乐部状态文本
  getClubStatusText(status: string) {
    const statusMap: { [key: string]: string } = {
      'active': '正常',
      'suspended': '暂停',
      'closed': '关闭'
    }
    return statusMap[status] || '未知'
  }
})
