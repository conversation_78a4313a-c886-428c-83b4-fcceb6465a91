# 数据库设计方案

## 1. 数据库集合设计

### 1.1 用户表 (users)
```javascript
{
  _id: "user_id",
  openid: "wx_openid",
  unionid: "wx_unionid", // 可选
  nickname: "用户昵称",
  avatar: "头像URL",
  phone: "手机号码",
  role: "companion|manager|admin", // 用户角色
  status: "active|inactive|banned", // 用户状态
  profile: {
    realName: "真实姓名",
    idCard: "身份证号", // 加密存储
    bankCard: "银行卡号", // 加密存储
    wechatId: "微信号"
  },
  settings: {
    maxClubs: 5, // 最大加入俱乐部数量
    notifications: true, // 通知设置
    autoUpdate: true // 自动更新设置
  },
  createdAt: "创建时间",
  updatedAt: "更新时间"
}
```

### 1.2 俱乐部表 (clubs)
```javascript
{
  _id: "club_id",
  name: "俱乐部名称",
  description: "俱乐部描述",
  logo: "俱乐部Logo URL",
  ownerId: "创建者用户ID",
  status: "active|inactive|closed", // 俱乐部状态
  settings: {
    maxMembers: 100, // 最大成员数
    commissionRate: 0.15, // 佣金比例
    autoApprove: false, // 自动审批加入
    allowMultipleClubs: true, // 允许成员加入多个俱乐部
    workingHours: {
      start: "09:00",
      end: "23:00"
    }
  },
  financial: {
    totalRevenue: 0, // 总收入
    totalCommission: 0, // 总佣金
    pendingSettlement: 0 // 待结算金额
  },
  memberCount: 0, // 当前成员数
  createdAt: "创建时间",
  updatedAt: "更新时间"
}
```

### 1.3 俱乐部成员表 (club_members)
```javascript
{
  _id: "member_id",
  clubId: "俱乐部ID",
  userId: "用户ID",
  role: "member|admin|owner", // 在俱乐部中的角色
  status: "active|pending|inactive", // 成员状态
  joinedAt: "加入时间",
  permissions: ["view_reports", "manage_members", "financial_access"], // 权限列表
  performance: {
    totalOrders: 0, // 总订单数
    totalRevenue: 0, // 总收入
    totalCommission: 0, // 总佣金
    rating: 5.0 // 评分
  },
  createdAt: "创建时间",
  updatedAt: "更新时间"
}
```

### 1.4 游戏订单表 (game_orders)
```javascript
{
  _id: "order_id",
  clubId: "俱乐部ID",
  companionId: "陪玩师用户ID",
  orderType: "gaming", // 订单类型
  gameInfo: {
    gameName: "游戏名称",
    gameMode: "游戏模式",
    duration: 120, // 游戏时长(分钟)
    rank: "段位要求"
  },
  clientInfo: {
    clientId: "客户ID", // 可选
    clientName: "客户昵称",
    clientContact: "客户联系方式"
  },
  financial: {
    totalAmount: 100.00, // 订单总金额
    companionEarning: 85.00, // 陪玩师收入
    clubCommission: 15.00, // 俱乐部佣金
    platformFee: 0.00 // 平台费用
  },
  status: "pending|in_progress|completed|cancelled", // 订单状态
  startTime: "开始时间",
  endTime: "结束时间",
  notes: "备注信息",
  attachments: ["图片URL数组"], // 附件
  createdAt: "创建时间",
  updatedAt: "更新时间"
}
```

### 1.5 礼物订单表 (gift_orders)
```javascript
{
  _id: "gift_order_id",
  clubId: "俱乐部ID",
  companionId: "陪玩师用户ID",
  orderType: "gift", // 订单类型
  giftInfo: {
    giftName: "礼物名称",
    giftType: "礼物类型",
    quantity: 1, // 数量
    unitPrice: 50.00 // 单价
  },
  clientInfo: {
    clientId: "客户ID",
    clientName: "客户昵称"
  },
  financial: {
    totalAmount: 50.00,
    companionEarning: 42.50,
    clubCommission: 7.50
  },
  status: "pending|confirmed|cancelled",
  screenshot: "截图URL", // 礼物截图
  verifiedAt: "验证时间",
  notes: "备注",
  createdAt: "创建时间",
  updatedAt: "更新时间"
}
```

### 1.6 罚款记录表 (fine_records)
```javascript
{
  _id: "fine_id",
  clubId: "俱乐部ID",
  companionId: "陪玩师用户ID",
  orderType: "fine",
  fineInfo: {
    reason: "罚款原因",
    category: "late|absent|violation|other", // 罚款类别
    amount: 20.00, // 罚款金额
    description: "详细描述"
  },
  issuedBy: "处罚人用户ID",
  status: "pending|paid|waived", // 罚款状态
  dueDate: "缴费截止日期",
  paidAt: "缴费时间",
  evidence: ["证据图片URL"], // 证据材料
  appeal: {
    submitted: false,
    reason: "申诉理由",
    submittedAt: "申诉时间",
    status: "pending|approved|rejected"
  },
  createdAt: "创建时间",
  updatedAt: "更新时间"
}
```

### 1.7 团费记录表 (group_fee_records)
```javascript
{
  _id: "fee_id",
  clubId: "俱乐部ID",
  companionId: "陪玩师用户ID",
  orderType: "group_fee",
  feeInfo: {
    period: "2024-01", // 费用周期
    amount: 100.00, // 团费金额
    feeType: "monthly|weekly|daily", // 费用类型
    description: "团费说明"
  },
  status: "pending|paid|overdue",
  dueDate: "缴费截止日期",
  paidAt: "缴费时间",
  paymentMethod: "wechat|alipay|bank", // 支付方式
  receipt: "收据URL", // 收据
  createdAt: "创建时间",
  updatedAt: "更新时间"
}
```

### 1.8 存单记录表 (deposit_records)
```javascript
{
  _id: "deposit_id",
  clubId: "俱乐部ID",
  companionId: "陪玩师用户ID",
  orderType: "deposit",
  depositInfo: {
    amount: 500.00, // 存款金额
    depositType: "security|advance|other", // 存款类型
    purpose: "保证金", // 存款用途
    interestRate: 0.0, // 利率
    maturityDate: "到期日期"
  },
  status: "active|withdrawn|expired",
  depositedAt: "存款时间",
  withdrawnAt: "取款时间",
  receipt: "存款凭证URL",
  notes: "备注信息",
  createdAt: "创建时间",
  updatedAt: "更新时间"
}
```

### 1.9 预存记录表 (predeposit_records)
```javascript
{
  _id: "predeposit_id",
  clubId: "俱乐部ID",
  companionId: "陪玩师用户ID",
  orderType: "predeposit",
  predepositInfo: {
    amount: 200.00, // 预存金额
    purpose: "游戏充值", // 预存用途
    gameAccount: "游戏账号",
    expectedUsage: "预计使用时间"
  },
  status: "pending|approved|used|refunded",
  approvedBy: "审批人用户ID",
  approvedAt: "审批时间",
  usedAt: "使用时间",
  refundedAt: "退款时间",
  evidence: ["凭证图片URL"],
  createdAt: "创建时间",
  updatedAt: "更新时间"
}
```

### 1.10 客户获取记录表 (client_acquisition_records)
```javascript
{
  _id: "acquisition_id",
  clubId: "俱乐部ID",
  companionId: "陪玩师用户ID",
  orderType: "client_acquisition",
  clientInfo: {
    clientName: "客户昵称",
    clientContact: "客户联系方式",
    clientSource: "客户来源",
    clientValue: "客户价值评估",
    gamePreference: "游戏偏好"
  },
  acquisitionInfo: {
    method: "推广方式",
    cost: 0.00, // 获客成本
    expectedRevenue: 1000.00, // 预期收入
    conversionRate: 0.8 // 转化率
  },
  reward: {
    amount: 50.00, // 奖励金额
    type: "cash|bonus|commission", // 奖励类型
    paidAt: "发放时间"
  },
  status: "pending|verified|rewarded",
  verifiedBy: "验证人用户ID",
  verifiedAt: "验证时间",
  notes: "备注信息",
  createdAt: "创建时间",
  updatedAt: "更新时间"
}
```

### 1.11 公告表 (announcements)
```javascript
{
  _id: "announcement_id",
  clubId: "俱乐部ID", // null表示全平台公告
  title: "公告标题",
  content: "公告内容",
  type: "general|urgent|system|financial", // 公告类型
  priority: "low|medium|high|critical", // 优先级
  publisherId: "发布者用户ID",
  targetAudience: "all|companions|managers", // 目标受众
  status: "draft|published|archived",
  publishedAt: "发布时间",
  expiresAt: "过期时间",
  attachments: ["附件URL"],
  readBy: ["已读用户ID数组"],
  createdAt: "创建时间",
  updatedAt: "更新时间"
}
```

### 1.12 财务结算表 (financial_settlements)
```javascript
{
  _id: "settlement_id",
  clubId: "俱乐部ID",
  companionId: "陪玩师用户ID",
  period: {
    start: "结算开始日期",
    end: "结算结束日期",
    type: "daily|weekly|monthly" // 结算周期
  },
  summary: {
    totalOrders: 10, // 总订单数
    totalRevenue: 1000.00, // 总收入
    totalCommission: 150.00, // 总佣金
    totalFines: 20.00, // 总罚款
    totalFees: 100.00, // 总费用
    netEarnings: 730.00 // 净收入
  },
  status: "pending|approved|paid",
  approvedBy: "审批人用户ID",
  approvedAt: "审批时间",
  paidAt: "支付时间",
  paymentMethod: "wechat|alipay|bank",
  receipt: "支付凭证URL",
  createdAt: "创建时间",
  updatedAt: "更新时间"
}
```

## 2. 数据库索引设计

### 2.1 主要索引
```javascript
// users集合
db.users.createIndex({ "openid": 1 }, { unique: true })
db.users.createIndex({ "phone": 1 })
db.users.createIndex({ "role": 1, "status": 1 })

// clubs集合
db.clubs.createIndex({ "ownerId": 1 })
db.clubs.createIndex({ "status": 1 })

// club_members集合
db.club_members.createIndex({ "clubId": 1, "userId": 1 }, { unique: true })
db.club_members.createIndex({ "userId": 1 })
db.club_members.createIndex({ "clubId": 1, "role": 1 })

// 订单相关集合的通用索引
db.game_orders.createIndex({ "clubId": 1, "companionId": 1 })
db.game_orders.createIndex({ "status": 1, "createdAt": -1 })
db.gift_orders.createIndex({ "clubId": 1, "companionId": 1 })
db.fine_records.createIndex({ "clubId": 1, "companionId": 1 })
```

## 3. 数据安全和权限

### 3.1 数据库权限规则
```javascript
// 用户只能访问自己的数据和所属俱乐部的数据
{
  "read": "auth != null && (resource.data.userId == auth.uid || get(/databases/$(database)/documents/club_members/$(auth.uid + '_' + resource.data.clubId)).data.status == 'active')",
  "write": "auth != null && resource.data.userId == auth.uid"
}
```

### 3.2 敏感数据加密
- 身份证号码
- 银行卡号
- 手机号码
- 财务相关数据
