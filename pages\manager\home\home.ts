// pages/manager/home/<USER>
import api from '../../../utils/api'
import { formatDate, formatAmount, getRelativeTime, showError } from '../../../utils/util'

const app = getApp<IAppOption>()

Page({
  data: {
    userInfo: null as any,
    currentClub: null as any,
    todayDate: '',
    todayStats: {
      totalOrders: 0,
      totalRevenue: '0.00',
      activeMembers: 0,
      pendingOrders: 0
    },
    pendingItems: [] as any[],
    weeklyStats: {
      totalRevenue: '0.00',
      growthRate: '0'
    },
    memberRanking: [] as any[],
    recentActivities: [] as any[],
    unreadCount: 0
  },

  onLoad() {
    this.initPage()
  },

  onShow() {
    this.refreshData()
  },

  onPullDownRefresh() {
    this.refreshData().finally(() => {
      wx.stopPullDownRefresh()
    })
  },

  // 初始化页面
  async initPage() {
    // 检查登录状态和权限
    const userInfo = wx.getStorageSync('userInfo')
    const userRole = wx.getStorageSync('userRole')
    
    if (!userInfo || userRole !== 'manager') {
      wx.reLaunch({
        url: '/pages/login/login'
      })
      return
    }

    this.setData({
      userInfo,
      todayDate: formatDate(new Date())
    })

    // 获取当前俱乐部
    const currentClub = app.globalData.currentClub
    if (currentClub) {
      this.setData({ currentClub })
      await this.refreshData()
    } else {
      // 如果没有俱乐部，跳转到俱乐部管理
      wx.redirectTo({
        url: '/pages/manager/club-manage/club-manage'
      })
    }
  },

  // 刷新数据
  async refreshData() {
    if (!this.data.currentClub) return

    await Promise.all([
      this.loadTodayStats(),
      this.loadPendingItems(),
      this.loadWeeklyStats(),
      this.loadMemberRanking(),
      this.loadRecentActivities()
    ])
  },

  // 加载今日统计
  async loadTodayStats() {
    try {
      const res = await api.callFunction('getManagerTodayStats', {
        clubId: this.data.currentClub.id
      }, false)
      
      if (res.success) {
        this.setData({
          todayStats: {
            totalOrders: res.data.totalOrders || 0,
            totalRevenue: formatAmount(res.data.totalRevenue || 0, false),
            activeMembers: res.data.activeMembers || 0,
            pendingOrders: res.data.pendingOrders || 0
          }
        })
      }
    } catch (error) {
      console.error('加载今日统计失败:', error)
    }
  },

  // 加载待处理事项
  async loadPendingItems() {
    try {
      const res = await api.callFunction('getPendingItems', {
        clubId: this.data.currentClub.id
      }, false)
      
      if (res.success) {
        const items = res.data.items || []
        this.setData({ 
          pendingItems: items.map((item: any) => ({
            ...item,
            icon: this.getPendingItemIcon(item.type)
          }))
        })
      }
    } catch (error) {
      console.error('加载待处理事项失败:', error)
    }
  },

  // 加载本周统计
  async loadWeeklyStats() {
    try {
      const res = await api.callFunction('getWeeklyStats', {
        clubId: this.data.currentClub.id
      }, false)
      
      if (res.success) {
        this.setData({
          weeklyStats: {
            totalRevenue: formatAmount(res.data.totalRevenue || 0, false),
            growthRate: (res.data.growthRate || 0).toFixed(1)
          }
        })
      }
    } catch (error) {
      console.error('加载本周统计失败:', error)
    }
  },

  // 加载成员排行
  async loadMemberRanking() {
    try {
      const res = await api.callFunction('getMemberRanking', {
        clubId: this.data.currentClub.id,
        limit: 5
      }, false)
      
      if (res.success) {
        const ranking = (res.data.ranking || []).map((member: any) => ({
          ...member,
          weeklyEarnings: formatAmount(member.weeklyEarnings || 0, false)
        }))
        
        this.setData({ memberRanking: ranking })
      }
    } catch (error) {
      console.error('加载成员排行失败:', error)
    }
  },

  // 加载最新动态
  async loadRecentActivities() {
    try {
      const res = await api.callFunction('getRecentActivities', {
        clubId: this.data.currentClub.id,
        limit: 10
      }, false)
      
      if (res.success) {
        const activities = (res.data.activities || []).map((activity: any) => ({
          ...activity,
          time: getRelativeTime(activity.createdAt)
        }))
        
        this.setData({ recentActivities: activities })
      }
    } catch (error) {
      console.error('加载最新动态失败:', error)
    }
  },

  // 获取待处理事项图标
  getPendingItemIcon(type: string) {
    const iconMap: { [key: string]: string } = {
      'order_review': '/assets/icons/review.png',
      'member_application': '/assets/icons/user-add.png',
      'appeal': '/assets/icons/appeal.png',
      'settlement': '/assets/icons/settlement.png'
    }
    return iconMap[type] || '/assets/icons/pending.png'
  },

  // 管理俱乐部
  manageClub() {
    wx.navigateTo({
      url: '/pages/manager/club-manage/club-manage'
    })
  },

  // 显示通知
  showNotifications() {
    wx.navigateTo({
      url: '/pages/manager/notifications/notifications'
    })
  },

  // 查看所有待处理事项
  viewAllPending() {
    wx.navigateTo({
      url: '/pages/manager/pending/pending'
    })
  },

  // 处理待处理事项
  handlePendingItem(e: any) {
    const item = e.currentTarget.dataset.item
    
    switch (item.type) {
      case 'order_review':
        wx.navigateTo({
          url: '/pages/manager/order-review/order-review'
        })
        break
      case 'member_application':
        wx.navigateTo({
          url: '/pages/manager/member-manage/member-manage'
        })
        break
      case 'appeal':
        wx.navigateTo({
          url: '/pages/manager/appeals/appeals'
        })
        break
      case 'settlement':
        wx.navigateTo({
          url: '/pages/manager/financial/financial'
        })
        break
    }
  },

  // 审核报单
  reviewOrders() {
    wx.navigateTo({
      url: '/pages/manager/order-review/order-review'
    })
  },

  // 发布公告
  publishAnnouncement() {
    wx.navigateTo({
      url: '/pages/manager/announcement/announcement'
    })
  },

  // 成员管理
  manageMembers() {
    wx.navigateTo({
      url: '/pages/manager/member-manage/member-manage'
    })
  },

  // 财务管理
  financialManagement() {
    wx.navigateTo({
      url: '/pages/manager/financial/financial'
    })
  },

  // 查看统计
  viewStatistics() {
    wx.navigateTo({
      url: '/pages/manager/statistics/statistics'
    })
  },

  // 查看完整排行
  viewFullRanking() {
    wx.navigateTo({
      url: '/pages/manager/ranking/ranking'
    })
  },

  // 查看成员详情
  viewMemberDetail(e: any) {
    const id = e.currentTarget.dataset.id
    wx.navigateTo({
      url: `/pages/manager/member-detail/member-detail?id=${id}`
    })
  },

  // 查看所有动态
  viewAllActivities() {
    wx.navigateTo({
      url: '/pages/manager/activities/activities'
    })
  }
})
