{"name": "companion-gaming-system", "version": "1.0.0", "description": "陪玩服务管理系统 - 微信小程序", "main": "app.ts", "scripts": {"deploy": "node scripts/deploy.js", "lint": "eslint . --ext .ts,.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["wechat", "miniprogram", "companion", "gaming", "management"], "author": "Your Name", "license": "MIT", "devDependencies": {"@typescript-eslint/eslint-plugin": "^5.0.0", "@typescript-eslint/parser": "^5.0.0", "eslint": "^8.0.0", "typescript": "^4.0.0"}, "repository": {"type": "git", "url": "https://github.com/your-username/companion-gaming-system.git"}, "bugs": {"url": "https://github.com/your-username/companion-gaming-system/issues"}, "homepage": "https://github.com/your-username/companion-gaming-system#readme"}