// pages/companion/order-detail/order-detail.ts
import api from '../../../utils/api'
import { formatTime, showError, showSuccess, showConfirm } from '../../../utils/util'

Page({
  data: {
    orderId: '',
    orderDetail: {} as any,
    showActions: false
  },

  onLoad(options: any) {
    if (options.id) {
      this.setData({ orderId: options.id })
      this.loadOrderDetail()
    } else {
      showError('订单ID不能为空')
      wx.navigateBack()
    }
  },

  // 加载订单详情
  async loadOrderDetail() {
    try {
      const res = await api.callFunction('getOrderDetail', {
        orderId: this.data.orderId
      })

      if (res.success) {
        const orderDetail = this.processOrderDetail(res.data.order)
        this.setData({ 
          orderDetail,
          showActions: ['pending', 'rejected'].includes(orderDetail.status)
        })
      }
    } catch (error) {
      console.error('加载订单详情失败:', error)
      showError('加载订单详情失败')
    }
  },

  // 处理订单详情数据
  processOrderDetail(order: any) {
    return {
      ...order,
      typeText: this.getOrderTypeText(order.orderType),
      statusText: this.getOrderStatusText(order.status),
      createdAt: formatTime(new Date(order.createdAt)),
      startTime: order.startTime ? formatTime(new Date(order.startTime)) : null,
      endTime: order.endTime ? formatTime(new Date(order.endTime)) : null,
      reviewInfo: order.reviewInfo ? {
        ...order.reviewInfo,
        reviewedAt: formatTime(new Date(order.reviewInfo.reviewedAt))
      } : null
    }
  },

  // 预览图片
  previewImage(e: any) {
    const url = e.currentTarget.dataset.url
    const urls = e.currentTarget.dataset.urls
    
    wx.previewImage({
      current: url,
      urls: urls
    })
  },

  // 编辑订单
  editOrder() {
    wx.navigateTo({
      url: `/pages/companion/create-order/create-order?type=${this.data.orderDetail.orderType}&id=${this.data.orderId}`
    })
  },

  // 删除订单
  async deleteOrder() {
    const confirmed = await showConfirm('确定要删除这个订单吗？', '删除确认')
    if (!confirmed) return

    try {
      const res = await api.callFunction('deleteOrder', {
        orderId: this.data.orderId
      })

      if (res.success) {
        showSuccess('删除成功')
        wx.navigateBack()
      }
    } catch (error) {
      console.error('删除订单失败:', error)
      showError('删除失败')
    }
  },

  // 重新提交订单
  async resubmitOrder() {
    try {
      const res = await api.callFunction('resubmitOrder', {
        orderId: this.data.orderId
      })

      if (res.success) {
        showSuccess('重新提交成功')
        this.loadOrderDetail()
      }
    } catch (error) {
      console.error('重新提交失败:', error)
      showError('重新提交失败')
    }
  },

  // 获取订单类型文本
  getOrderTypeText(type: string) {
    const typeMap: { [key: string]: string } = {
      'gaming': '游戏单',
      'gift': '礼物单',
      'fine': '罚款信息',
      'group_fee': '团费信息',
      'deposit': '存单信息',
      'predeposit': '预存信息',
      'client_acquisition': '拉老板信息'
    }
    return typeMap[type] || '未知类型'
  },

  // 获取订单状态文本
  getOrderStatusText(status: string) {
    const statusMap: { [key: string]: string } = {
      'pending': '待审核',
      'approved': '已通过',
      'rejected': '已拒绝',
      'in_progress': '进行中',
      'completed': '已完成',
      'cancelled': '已取消'
    }
    return statusMap[status] || '未知状态'
  }
})
