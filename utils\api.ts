// API 工具类
import { showLoading, hideLoading, showError } from './util'

interface ApiResponse {
  success: boolean
  data?: any
  message?: string
  code?: number
}

class ApiService {
  // 调用云函数
  async callFunction(name: string, data: any = {}, showLoadingTip: boolean = true): Promise<ApiResponse> {
    if (showLoadingTip) {
      showLoading()
    }

    try {
      const res = await wx.cloud.callFunction({
        name,
        data
      })

      if (showLoadingTip) {
        hideLoading()
      }

      if (res.result && res.result.success) {
        return {
          success: true,
          data: res.result.data,
          message: res.result.message
        }
      } else {
        const errorMsg = res.result?.message || '请求失败'
        showError(errorMsg)
        return {
          success: false,
          message: errorMsg
        }
      }
    } catch (error: any) {
      if (showLoadingTip) {
        hideLoading()
      }
      
      const errorMsg = error.message || '网络错误'
      showError(errorMsg)
      return {
        success: false,
        message: errorMsg
      }
    }
  }

  // 用户相关API
  async login(code: string) {
    return this.callFunction('login', { code })
  }

  async getUserInfo() {
    return this.callFunction('getUserInfo')
  }

  async updateUserInfo(userInfo: any) {
    return this.callFunction('updateUserInfo', { userInfo })
  }

  async getUserClubs() {
    return this.callFunction('getUserClubs')
  }

  // 俱乐部相关API
  async createClub(clubInfo: any) {
    return this.callFunction('createClub', { clubInfo })
  }

  async updateClub(clubId: string, clubInfo: any) {
    return this.callFunction('updateClub', { clubId, clubInfo })
  }

  async getClubInfo(clubId: string) {
    return this.callFunction('getClubInfo', { clubId })
  }

  async joinClub(clubId: string, inviteCode?: string) {
    return this.callFunction('joinClub', { clubId, inviteCode })
  }

  async leaveClub(clubId: string) {
    return this.callFunction('leaveClub', { clubId })
  }

  async getClubMembers(clubId: string) {
    return this.callFunction('getClubMembers', { clubId })
  }

  async updateMemberRole(clubId: string, userId: string, role: string) {
    return this.callFunction('updateMemberRole', { clubId, userId, role })
  }

  async removeMember(clubId: string, userId: string) {
    return this.callFunction('removeMember', { clubId, userId })
  }

  // 订单相关API
  async createOrder(orderData: any) {
    return this.callFunction('createOrder', { orderData })
  }

  async updateOrder(orderId: string, orderData: any) {
    return this.callFunction('updateOrder', { orderId, orderData })
  }

  async getOrders(clubId: string, filters: any = {}) {
    return this.callFunction('getOrders', { clubId, filters })
  }

  async getOrderDetail(orderId: string) {
    return this.callFunction('getOrderDetail', { orderId })
  }

  async approveOrder(orderId: string, note?: string) {
    return this.callFunction('approveOrder', { orderId, note })
  }

  async rejectOrder(orderId: string, reason: string) {
    return this.callFunction('rejectOrder', { orderId, reason })
  }

  async deleteOrder(orderId: string) {
    return this.callFunction('deleteOrder', { orderId })
  }

  // 游戏订单
  async createGameOrder(gameOrderData: any) {
    return this.callFunction('createGameOrder', { gameOrderData })
  }

  // 礼物订单
  async createGiftOrder(giftOrderData: any) {
    return this.callFunction('createGiftOrder', { giftOrderData })
  }

  // 罚款记录
  async createFineRecord(fineData: any) {
    return this.callFunction('createFineRecord', { fineData })
  }

  async payFine(fineId: string, paymentData: any) {
    return this.callFunction('payFine', { fineId, paymentData })
  }

  // 团费记录
  async createGroupFee(feeData: any) {
    return this.callFunction('createGroupFee', { feeData })
  }

  async payGroupFee(feeId: string, paymentData: any) {
    return this.callFunction('payGroupFee', { feeId, paymentData })
  }

  // 存单记录
  async createDeposit(depositData: any) {
    return this.callFunction('createDeposit', { depositData })
  }

  async withdrawDeposit(depositId: string) {
    return this.callFunction('withdrawDeposit', { depositId })
  }

  // 预存记录
  async createPredeposit(predepositData: any) {
    return this.callFunction('createPredeposit', { predepositData })
  }

  async approvePredeposit(predepositId: string) {
    return this.callFunction('approvePredeposit', { predepositId })
  }

  // 客户获取记录
  async createClientAcquisition(clientData: any) {
    return this.callFunction('createClientAcquisition', { clientData })
  }

  async verifyClientAcquisition(acquisitionId: string) {
    return this.callFunction('verifyClientAcquisition', { acquisitionId })
  }

  // 公告相关API
  async getAnnouncements(clubId?: string) {
    return this.callFunction('getAnnouncements', { clubId })
  }

  async createAnnouncement(announcementData: any) {
    return this.callFunction('createAnnouncement', { announcementData })
  }

  async updateAnnouncement(announcementId: string, announcementData: any) {
    return this.callFunction('updateAnnouncement', { announcementId, announcementData })
  }

  async deleteAnnouncement(announcementId: string) {
    return this.callFunction('deleteAnnouncement', { announcementId })
  }

  async markAnnouncementRead(announcementId: string) {
    return this.callFunction('markAnnouncementRead', { announcementId })
  }

  // 财务相关API
  async getEarningsStats(clubId: string, period: any) {
    return this.callFunction('getEarningsStats', { clubId, period })
  }

  async getFinancialSummary(clubId: string, userId?: string) {
    return this.callFunction('getFinancialSummary', { clubId, userId })
  }

  async createSettlement(settlementData: any) {
    return this.callFunction('createSettlement', { settlementData })
  }

  async approveSettlement(settlementId: string) {
    return this.callFunction('approveSettlement', { settlementId })
  }

  async getSettlements(clubId: string, userId?: string) {
    return this.callFunction('getSettlements', { clubId, userId })
  }

  // 统计相关API
  async getDashboardStats(clubId: string) {
    return this.callFunction('getDashboardStats', { clubId })
  }

  async getOrderStats(clubId: string, period: any) {
    return this.callFunction('getOrderStats', { clubId, period })
  }

  async getMemberStats(clubId: string) {
    return this.callFunction('getMemberStats', { clubId })
  }

  // 文件上传
  async uploadFile(filePath: string, cloudPath: string): Promise<ApiResponse> {
    showLoading('上传中...')
    
    try {
      const result = await wx.cloud.uploadFile({
        cloudPath,
        filePath
      })

      hideLoading()
      
      return {
        success: true,
        data: {
          fileID: result.fileID,
          cloudPath: cloudPath
        }
      }
    } catch (error: any) {
      hideLoading()
      showError('上传失败')
      return {
        success: false,
        message: error.message || '上传失败'
      }
    }
  }

  // 删除文件
  async deleteFile(fileIDs: string[]): Promise<ApiResponse> {
    try {
      const result = await wx.cloud.deleteFile({
        fileList: fileIDs
      })

      return {
        success: true,
        data: result
      }
    } catch (error: any) {
      return {
        success: false,
        message: error.message || '删除失败'
      }
    }
  }
}

// 导出单例
export const api = new ApiService()
export default api
