// pages/companion/profile/profile.ts
import api from '../../../utils/api'
import { formatDate, formatAmount, showError, showSuccess, showConfirm } from '../../../utils/util'

const app = getApp<IAppOption>()

Page({
  data: {
    userInfo: {} as any,
    joinDate: '',
    userStats: {
      totalOrders: 0,
      totalEarnings: '0.00',
      clubCount: 0,
      rating: '5.0'
    },
    unreadCount: 0,
    version: '1.0.0',
    
    // 编辑资料
    showEditModal: false,
    editForm: {
      realName: '',
      phone: '',
      wechatId: ''
    },
    saving: false
  },

  onLoad() {
    this.loadUserProfile()
  },

  onShow() {
    this.loadUserStats()
    this.loadUnreadCount()
  },

  // 加载用户资料
  async loadUserProfile() {
    const userInfo = wx.getStorageSync('userInfo')
    if (userInfo) {
      this.setData({ 
        userInfo,
        joinDate: formatDate(new Date(userInfo.createdAt || Date.now()))
      })
      
      // 加载详细资料
      await this.loadUserDetail()
    }
  },

  // 加载用户详细信息
  async loadUserDetail() {
    try {
      const res = await api.callFunction('getUserDetail', {}, false)
      if (res.success) {
        const profile = res.data.profile || {}
        this.setData({
          editForm: {
            realName: profile.realName || '',
            phone: profile.phone || '',
            wechatId: profile.wechatId || ''
          }
        })
      }
    } catch (error) {
      console.error('加载用户详细信息失败:', error)
    }
  },

  // 加载用户统计
  async loadUserStats() {
    try {
      const res = await api.callFunction('getUserStats', {}, false)
      if (res.success) {
        const stats = res.data.stats
        this.setData({
          userStats: {
            totalOrders: stats.totalOrders || 0,
            totalEarnings: formatAmount(stats.totalEarnings || 0, false),
            clubCount: stats.clubCount || 0,
            rating: (stats.rating || 5.0).toFixed(1)
          }
        })
      }
    } catch (error) {
      console.error('加载用户统计失败:', error)
    }
  },

  // 加载未读消息数
  async loadUnreadCount() {
    try {
      const res = await api.callFunction('getUnreadCount', {}, false)
      if (res.success) {
        this.setData({ unreadCount: res.data.count || 0 })
      }
    } catch (error) {
      console.error('加载未读消息数失败:', error)
    }
  },

  // 编辑资料
  editProfile() {
    this.setData({ showEditModal: true })
  },

  // 隐藏编辑弹窗
  hideEditModal() {
    this.setData({ showEditModal: false })
  },

  // 阻止事件冒泡
  stopPropagation() {},

  // 输入事件
  onRealNameInput(e: any) {
    this.setData({ 'editForm.realName': e.detail.value })
  },

  onPhoneInput(e: any) {
    this.setData({ 'editForm.phone': e.detail.value })
  },

  onWechatIdInput(e: any) {
    this.setData({ 'editForm.wechatId': e.detail.value })
  },

  // 保存资料
  async saveProfile() {
    const { realName, phone, wechatId } = this.data.editForm
    
    // 简单验证
    if (phone && !/^1[3-9]\d{9}$/.test(phone)) {
      showError('请输入正确的手机号码')
      return
    }

    this.setData({ saving: true })

    try {
      const res = await api.callFunction('updateUserProfile', {
        profile: {
          realName,
          phone,
          wechatId
        }
      })

      if (res.success) {
        showSuccess('保存成功')
        this.hideEditModal()
      }
    } catch (error) {
      console.error('保存资料失败:', error)
      showError('保存失败')
    } finally {
      this.setData({ saving: false })
    }
  },

  // 查看俱乐部
  viewClubs() {
    wx.navigateTo({
      url: '/pages/companion/clubs/clubs'
    })
  },

  // 查看收益
  viewEarnings() {
    wx.switchTab({
      url: '/pages/companion/earnings/earnings'
    })
  },

  // 查看订单
  viewOrders() {
    wx.switchTab({
      url: '/pages/companion/orders/orders'
    })
  },

  // 查看通知
  viewNotifications() {
    wx.navigateTo({
      url: '/pages/companion/notifications/notifications'
    })
  },

  // 查看设置
  viewSettings() {
    wx.navigateTo({
      url: '/pages/companion/settings/settings'
    })
  },

  // 查看帮助
  viewHelp() {
    wx.navigateTo({
      url: '/pages/companion/help/help'
    })
  },

  // 查看关于
  viewAbout() {
    wx.navigateTo({
      url: '/pages/companion/about/about'
    })
  },

  // 退出登录
  async logout() {
    const confirmed = await showConfirm('确定要退出登录吗？', '退出确认')
    if (!confirmed) return

    try {
      // 清除本地数据
      wx.clearStorageSync()
      
      // 重置全局数据
      app.globalData.userInfo = undefined
      app.globalData.userRole = undefined
      app.globalData.currentClub = undefined
      app.globalData.clubs = []

      showSuccess('已退出登录')
      
      // 跳转到登录页
      wx.reLaunch({
        url: '/pages/login/login'
      })
    } catch (error) {
      console.error('退出登录失败:', error)
      showError('退出失败')
    }
  }
})
